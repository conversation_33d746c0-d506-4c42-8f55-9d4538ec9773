FROM node:20-alpine3.20

ARG PROJECT
ENV PROJECT_NAME=$PROJECT \
    PNPM_HOME=/root/.pnpm-store \
    NPM_CONFIG_REGISTRY=https://registry.npmmirror.com

WORKDIR /app

RUN corepack enable && corepack prepare pnpm@8.6.6 --activate
RUN npm install -g ts-node

# 复制 workspace 配置和根目录的包管理文件
COPY pnpm-workspace.yaml package.json ./

# 复制所有 package.json 文件，保持目录结构
COPY apps/moer_overseas/package.json ./apps/moer_overseas/
COPY apps/yuhe/package.json ./apps/yuhe/
COPY apps/haogu/package.json ./apps/haogu/
COPY packages/config/package.json ./packages/config/
COPY packages/lib/package.json ./packages/lib/
COPY packages/model/package.json ./packages/model/
COPY packages/service/package.json ./packages/service/

RUN pnpm config set registry https://registry.npmmirror.com && npm config set registry https://registry.npmmirror.com
RUN pnpm install

COPY . .

RUN pnpm run prisma_generate

# 根据项目设置工作目录并启动
ENTRYPOINT [ "ts-node" ]