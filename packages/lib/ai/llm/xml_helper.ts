import { XMLHelper } from '../../xml/xml'
import { LLM } from './llm_model'
import { OpenAIModelName } from './client'

export class LLMXMLHelper {

  public static async extractBooleanAnswer(res: string, options: { tagName?: string;  trueFlag: string; falseFlag: string}): Promise<boolean | null> {
    const xmlRes = XMLHelper.extractContent(res, options?.tagName || 'answer')

    if (!xmlRes) {
      return null
    }

    if (xmlRes.trim().toLowerCase() === options.trueFlag.toLowerCase().trim()) {
      return true
    } else if (xmlRes.trim().toLowerCase() === options.falseFlag.toLowerCase().trim()) {
      return false
    } else {
      return null
    }
  }

  public static async predictAndExtractBooleanAnswer(prompt: string, options: { tagName?: string;  trueFlag: string; falseFlag: string; model?: OpenAIModelName; meta?: Record<string, any>}): Promise<boolean | null> {
    const llm = new LLM({ model: options.model, meta: options.meta, temperature: 0 })
    const llmRes = await llm.predict(prompt)

    return LLMXMLHelper.extractBooleanAnswer(llmRes, options)
  }


  /**
   *尝试从 LLM 返回的文本中提取指定 tagName 的 XML 内容，如果失败则进行最多 3 次重试。
   * 如果提供了 fallbackHandler，当解析失败时会调用此函数去生成纠正信息并附加到 Prompt 后再次尝试。
   *
   * @param prompt 初始提示信息
   * @param options 配置选项：包含 tagName、可选的 model 与 meta 信息
   * @param fallbackHandler 当解析失败时，可选的回调函数，用于生成纠正信息并附加到 Prompt 后
   * @returns 如果提取成功则返回 XML 中的内容，否则返回 null
   */
  public static async extractXMLContentWithErrorHandler(
    prompt: string,
    options: {
      tagName: string
      model?: OpenAIModelName
      meta?: Record<string, any>
    },
    fallbackHandler?: (response: string) => string
  ): Promise<string | null> {
    const llm = new LLM({ model: options.model, meta: options.meta })

    let attemptPrompt = prompt
    let retryCount = 0

    while (retryCount < 3) {
      // 1. 调用 LLM
      const llmRes = await llm.predict(attemptPrompt)

      // 2. 提取 XML 内容
      const xmlRes = XMLHelper.extractContent(llmRes, options.tagName)
      if (xmlRes) {
        // 如果提取到内容，则直接返回
        return xmlRes
      }

      // 3. 如果提取失败并且存在 fallbackHandler，则调用它生成纠正信息并追加到 Prompt
      if (fallbackHandler) {
        const correctionMessage = fallbackHandler(llmRes)
        // 这里的处理你可以根据实际需求修改，比如加一些更具体的上下文或说明
        attemptPrompt = `${prompt}\n${correctionMessage}`
      } else {
        attemptPrompt = `${prompt}
Check your xml output and make sure it conforms, use <${options.tagName}> and </${options.tagName}> tags to enclose the answer`
      }

      retryCount++
    }

    // 超过 3 次重试后仍然无法提取到，则返回 null
    return null
  }
}