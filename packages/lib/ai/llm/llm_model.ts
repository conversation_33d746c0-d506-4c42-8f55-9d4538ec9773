import { AzureOpenAIClient, CheapOpenAI, OpenAIClient, OpenAIModelName, QwenMax } from './client'
import { BaseMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { ChatPromptTemplate, ParamsFromFString, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { Config } from '../../../config'
import { IterableReadableStream } from '@langchain/core/utils/stream'
import { Runnable, RunnableConfig } from '@langchain/core/runnables'
import { StringHelper } from '../../string'
import { StringOutputParser } from '@langchain/core/output_parsers'
import {
  llmImageChatCounter,
  llmImageChatErrorCounter,
  llmMessagePredictCounter,
  llmMessagePredictErrorCounter
} from './prometheus'

interface LLMInitParams {
  model?: OpenAIModelName
  temperature?: number
  maxTokens?: number
  responseJSON?: boolean  // 若设置为 true，系统提示词中必须包含 JSON 字样，如：请严格按照如下 JSON 格式输出
  systemPrompt?: string
  runnableConfig?: RunnableConfig
  promptName?: string
  meta?: Record<string, any>  // 用于 langSmith 记录元信息，方便查询，round_id 对应了 run_id， chat_id 对应了 thread_id
  projectName?: string  // 用于配置 langSmith 日志记录到的项目
}

enum ClientType {
  AZURE = 'azure',
  OPENAI = 'openai',
  CHEAP = 'cheap',
  QWEN = 'qwen'
}

/**
 * OpenAI API 封装
 * 注意：
 * 1. 聊天记录需自己维护
 * 2. 尽量不要引入 openai 包，所有功能尽量以 LangChain 实现，保证稳定性
 */
export class LLM {
  private readonly model: OpenAIModelName
  private readonly temperature: number
  private readonly maxTokens: number
  private readonly responseJSON: boolean | undefined
  private readonly systemPrompt?: string
  private readonly runId?: string
  private readonly runnableConfig: RunnableConfig

  constructor(params?: LLMInitParams) {
    // 标准化 meta
    const meta = {
      ...(params?.meta ?? {}),
      ...(params?.promptName ? { promptName: params.promptName } : {}),
      ...(params?.meta?.name ? { promptName: params.meta.name } : {}),
      ...(params?.meta?.chat_id ? { thread_id: params.meta.chat_id } : {})
    }

    // 设置 LangSmith 环境变量（项目名优先 params，其次配置）
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_PROJECT = params?.projectName ?? Config.setting.projectName
    process.env.LANGCHAIN_TRACING_V2 = 'true'

    // 字段赋值（使用 ?? 提供默认值）
    this.model = params?.model ?? 'gpt-4.1'
    this.temperature = params?.temperature ?? 0
    this.maxTokens = params?.maxTokens ?? 1024
    this.responseJSON = params?.responseJSON
    this.systemPrompt = params?.systemPrompt

    // 处理 runnableConfig 与元数据
    this.runnableConfig = params?.runnableConfig ?? {}
    this.runnableConfig.metadata = meta

    // 提取 runId
    this.runId = params?.meta?.round_id
  }

  private createClient(type: ClientType) {
    switch (type) {
      case ClientType.AZURE:
        return AzureOpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature,
          maxTokens: this.maxTokens
        })
      case ClientType.OPENAI:
        return OpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature
        })
      case ClientType.CHEAP:
        return CheapOpenAI.getClient({
          model: this.model,
          temperature: this.temperature
        })
      case ClientType.QWEN:
        return QwenMax.getClient(this.temperature)
      default:
        console.warn('Unknown client type:', type)
        // 保底按照 Azure OpenAI
        return AzureOpenAIClient.getClient({
          model: this.model,
          temperature: this.temperature,
          maxTokens: this.maxTokens
        })
    }
  }

  /**
   * 根据 model 参数调整调用优先级
   * @private
   */
  // 定义客户端类型枚举

  private getClients() {
    // 根据条件确定客户端顺序
    let clientOrder: ClientType[]

    if (this.model.startsWith('gpt') && !Config.setting.localTest) {
      clientOrder = [ClientType.AZURE, ClientType.CHEAP, ClientType.QWEN, ClientType.OPENAI]
    } else if (this.model.startsWith('claude')) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.QWEN, ClientType.OPENAI]
    } else if (this.model.startsWith('qwen-max')) {
      clientOrder = [ClientType.QWEN, ClientType.AZURE, ClientType.CHEAP, ClientType.OPENAI]
    } else if (Config.setting.localTest) {
      clientOrder = [ClientType.CHEAP, ClientType.AZURE, ClientType.QWEN, ClientType.OPENAI]
    } else { // 保底配置
      clientOrder = [ClientType.AZURE, ClientType.CHEAP, ClientType.QWEN, ClientType.OPENAI]
    }

    // 按确定的顺序返回客户端
    return clientOrder.map(this.createClient.bind(this))
  }

  /**
   * 使用文本或 PromptTemplate 调用 LLM
   * LLM.predict('Hello, World!')
   * LLM.predict(PromptTemplate.from('Hello, {name}!'), { name: 'World' })
   * @param text 文本 或 PromptTemplate, 如果是 PromptTemplate，则使用 params 参数替换模板中的变量
   * @param promptParams
   */
  async predict<T extends string>(
    text: string | SystemMessagePromptTemplate<ParamsFromFString<T>> | Runnable,
    promptParams?: ParamsFromFString<T>
  ): Promise<string> {
    const clients = this.getClients()
    const parser = new StringOutputParser()

    for (const client of clients) {
      try {
        llmMessagePredictCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        const runnableConfig = this.responseJSON ? {
          ...this.runnableConfig, response_format: { type: 'json_object' }
        } : this.runnableConfig

        if (typeof text === 'string') {
          return await client
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(text, { runId: this.runId })
        } else if (promptParams) {
          return await text
            .pipe(client)
            .pipe(parser)
            .withConfig(runnableConfig)
            .invoke(promptParams, { runId: this.runId })
        }
      } catch (e) {
        llmMessagePredictErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 简单使用单条文本调用 OpenAI
   */
  public static async predict<T extends string>(
    text: string | SystemMessagePromptTemplate<ParamsFromFString<T>> | Runnable,
    params?: LLMInitParams,
    promptParams?: ParamsFromFString<T>
  ): Promise<string> {
    const llm = new LLM(params)
    return await llm.predict(text, promptParams) as string
  }

  private isMessagesContainSystemPrompt(messages: BaseMessage[]): boolean {
    return messages.length > 0 && messages[0].getType() === 'system'
  }

  /**
   * 使用一组消息或 ChatPromptTemplate 调用 LLM，需要自己维护聊天记录
   * const messages = [
   *   new SystemMessage("You are a helpful assistant"),
   *   new HumanMessage("Tell me a joke about {topic}"),
   * ]
   * LLM.predictMessage(messages)
   *
   * const promptTemplate = ChatPromptTemplate.fromMessages([
   *   ["system", "You are a helpful assistant"],
   *   ["user", "Tell me a joke about {topic}"],
   * ])
   * LLM.predictMessage(promptTemplate, {topic: 'fuck'})
   * @param messages
   * @param params
   */
  async predictMessage<T extends BaseMessage [] | ChatPromptTemplate>(messages: T, params?: T extends ChatPromptTemplate<infer P> ? P : never): Promise<string> {
    const chatHistory = messages

    const parser = new StringOutputParser()

    const clients =  this.getClients()
    for (const client of clients) {
      try {
        if (Array.isArray(chatHistory)) {
          return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
        } else {
          return await (messages as ChatPromptTemplate).pipe(client).pipe(parser).withConfig(this.runnableConfig).invoke(params, { runId: this.runId })
        }
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
   * 使用一组消息调用 OpenAI，需要自己维护聊天记录
   * @param messages
   * @param params
   */
  public static async predictMessage(messages: BaseMessage[], params?: LLMInitParams): Promise<string> {
    const llm = new LLM(params)
    return await llm.predictMessage(messages) as string
  }

  /**
   * 流式输出，返回一个可迭代的流，包含多个 string 输出
   * 例如：
   * for await (const chunk of stream) {
   *   console.log(chunk);
   * }
   *
   * 输出：
   *   Hello
   *   !
   *   How
   *   can
   *   I
   *   assist
   *   you
   *   today
   *   ?
   *   参考： https://js.langchain.com/docs/modules/model_io/models/chat/how_to/streaming
   */
  async stream(messages: BaseMessage[], systemPrompt?: string): Promise<IterableReadableStream<string>> {
    let chatHistory = this.cleanPromptMessages(messages)
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...messages]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(messages)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...messages]
    }
    const parser = new StringOutputParser()

    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).stream(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)
      }
    }

    throw new Error('All attempts to stream with available clients have failed.')
  }

  cleanPromptMessage(prompt: string) {
    return StringHelper.replaceMultipleBlankLines(prompt).trim()
  }

  async imageChat(imageUrl: string, systemPrompt?: string) {
    const message = new HumanMessage({
      content: [
        { type: 'image_url', image_url: { url: imageUrl } }
      ]
    })
    let chatHistory: BaseMessage[] = [message]
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...chatHistory]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(chatHistory)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...chatHistory]
    }
    const parser = new StringOutputParser()
    const clients = this.getClients()

    for (let i = 0; i < clients.length; i++) {
      const client = clients[i]
      try {
        llmImageChatCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
      } catch (e) {
        llmImageChatErrorCounter.labels({ bot_id:Config.setting.wechatConfig?.name ?? Config.setting.wechatConfig?.name }).inc(1)
        console.error(`Error using client ${i} for image processing:`, e)

        // 如果是最后一个客户端，抛出错误
        if (i === clients.length - 1) {
          throw new Error('Failed to process image with all available clients.')
        }
      // 否则继续尝试下一个客户端
      }
    }
  }

  /**
   * Prompt 格式化，移除无用空行
   * @param messages
   * @private
   */
  private cleanPromptMessages(messages: BaseMessage[]) {
    messages.forEach((message) => {
      if (typeof message.content === 'string') {
        message.content = this.cleanPromptMessage(message.content)
      }
    })

    return messages
  }
}