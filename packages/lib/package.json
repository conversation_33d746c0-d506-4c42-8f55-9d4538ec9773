{"name": "lib", "private": true, "scripts": {"tsc-check": "tsc --noEmit"}, "devDependencies": {"@types/ali-oss": "^6.0.8", "@types/crypto-js": "^4.2.2", "@types/jest": "^30.0.0", "@types/mime": "^3.0.3", "@types/node": "^20.7.0", "@types/papaparse": "^5.3.15", "csv-parse": "^4.16.3", "jest": "^29.7.0", "langsmith": "^0.3.58", "papaparse": "^5.4.1", "typescript": "5.8.2"}, "dependencies": {"@elastic/elasticsearch": "^8.13.1", "@langchain/community": "^0.3.50", "@langchain/core": "^0.3.70", "@langchain/openai": "^0.6.7", "ali-oss": "^6.20.0", "axios": "^1.9.0", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "fast-xml-parser": "^4.3.5", "ioredis": "~5.4.1", "jsonrepair": "^3.6.0", "lru-cache": "^10.2.0", "mime": "^3.0.0", "openai": "^4.98.0", "openai-zod-functions": "^0.1.2", "pickleparser": "^0.2.1", "short-uuid": "^4.2.2", "zod": "^3.23.8", "prom-client": "^15.1.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}