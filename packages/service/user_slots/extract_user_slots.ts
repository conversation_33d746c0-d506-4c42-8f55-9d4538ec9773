import { HumanMessage, SystemMessage } from '@langchain/core/messages'

import logger from 'model/logger/logger'
import { ChatPromptTemplate, SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { XMLHelper } from 'lib/xml/xml'
import { ChatHistoryService } from '../chat_history/chat_history'
import dayjs from 'dayjs'
import { contentWithFrequency, ICustomSlot } from '../local_cache/type'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { LLM } from 'lib/ai/llm/llm_model'
import { OpenAIModelName } from 'lib/ai/llm/client'

export interface ILogInfo {
  [key:string]: any
  chat_id?: string
  user_id?: string
  round_id?: string
}

export type ChatHistoryWithRoleAndDate = {
  role: 'user' | 'assistant'
  date: string
  message: string
}

export class UserSlot {
  topic: string
  subTopic: string
  content: string
  frequency: number

  constructor(topic: string, subTopic: string, content: string, frequency: number = 1) {
    this.topic = topic
    this.subTopic = subTopic
    this.content = content
    this.frequency = frequency
  }

  static async fromString(input:string, logInfo?: ILogInfo): Promise<UserSlot | null> {
    const trimedInput = input.trim()
    if (trimedInput == '') return null
    if (!this.validateFormat(trimedInput)) {
      return await this.repairSlotForm(trimedInput, logInfo)
    }
    return this.fromCorrectInput(trimedInput)
  }

  private static fromCorrectInput(input: string): UserSlot| null {
    const trimedInput = input.trim()
    const originText = trimedInput.substring(2)
    const splited = originText.split('::')
    if (splited.length < 3) return null
    return new UserSlot(splited[0], splited[1], splited.slice(2).join(','))
  }

  private static async repairSlotForm(input: string, logInfo?: ILogInfo): Promise<UserSlot | null> {
    const llm = new LLM({
      meta: {
        ...logInfo,
        promptName: '修复槽位提取原始句子',
      },
    })
    const response = await llm.predictMessage([new SystemMessage(getRepairPrompt()), new HumanMessage(input)])
    if (!this.validateFormat(response)) {
      logger.error(`槽位初始语句修复失败，语句为:${response},logInfo:${logInfo}`)
      return null
    } else {
      return this.fromCorrectInput(response)
    }

    function getRepairPrompt():string {
      return `你是一位善于分析文本内容的专家。我将提供给你一段文本，请你认真分析，并按照以下格式进行输出：
## 格式
### 输出
你需要从对话中提取事实和偏好，并输出：
- TOPIC::SUB_TOPIC::MEMO
例如：
- 基本信息::姓名::melinda

输出一行，包括一个事实或偏好，包含：
1. TOPIC: 主题，表示该偏好的类别
2. SUB_TOPIC: 详细主题，表示该偏好的具体类别
3. MEMO: 提取的信息、事实或偏好

请先提炼出文本的核心主题，再根据主题明确地划分出相关的子主题，最后对子主题详细概括相应的内容。
请直接给出最终结果，无需解释分析过程。

## 注意
- 请只输出一个主题/子主题
- 请严格按照格式输出`
    }
  }

  private static validateFormat(input: string) :boolean {
    const trimedInput = input.trim()
    if (trimedInput.length < 2 && !trimedInput.startsWith('- ')) {
      return false
    }
    if (trimedInput.split('::').length < 3) return false

    return true
  }
}

export class UserSlots {
  slots: Record<string, Record<string, contentWithFrequency>> = {}
  constructor(userSlots: UserSlot[]) {
    for (const slot of userSlots) {
      this.add(slot)
    }
  }

  public static async getUserSlotSubTopicContent(chatStateStoreClient:ChatStateStore, chatId: string, topic:string, subTopic:string): Promise<string> {
    try {
      const userSlots = UserSlots.fromRecord((await chatStateStoreClient.get(chatId)).userSlots)
      return userSlots.getSubTopicContent(topic, subTopic)
    } catch (e) {
      logger.error(e)
      return ''
    }
  }

  getSubTopicContent(topic:string, subTopic:string): string {
    return (this.slots?.[topic]?.[subTopic]?.content) ?? ''
  }

  isTopicExist(topic:string):boolean {
    if (!this.slots[topic]) {
      return false
    } else {
      return true
    }
  }

  isTopicSubTopicExist(topic:string, subTopic:string):boolean {
    if (!this.slots[topic]) {
      return false
    }
    if (!this.slots[topic][subTopic]) {
      return false
    }
    return true
  }

  getStringByTopic(topic: string):string {
    if (!this.slots?.[topic]) return ''
    const res = Object.entries(this.slots[topic]).map(([key, value]) => `${key}::${value.content}`).join('，')
    return `${topic}：${res}`
  }

  static fromRecord(record: Record<string, contentWithFrequency>): UserSlots {
    const userSlotList:UserSlot[] = []
    for (const key in record) {
      const splitedKey = key.split('::')
      if (splitedKey.length != 2) {
        logger.error(`解析错误, ${key}`)
      } else {
        userSlotList.push({
          topic: splitedKey[0],
          subTopic: splitedKey[1],
          content: record[key].content,
          frequency: record[key].frequency
        })
      }
    }
    return new UserSlots(userSlotList)
  }

  toString(): string {
    let res:string = ''
    const importantThreshold = this.getImportantThreshold()
    for (const topic in this.slots) {
      res += `- ${topic}\n`
      for (const subTopic in this.slots[topic]) {
        const { content, frequency } = this.slots[topic][subTopic]
        res += `  - ${subTopic}`
        if (frequency > importantThreshold) {
          res += '（多次提及）'
        }
        res += `：${content}\n`
      }
    }
    return res ? res.trim() : '无'
  }

  getImportantThreshold(): number {
    let totalFrequency = 0
    let totalSubTopic = 0
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        const { frequency } = this.slots[topic][subTopic]
        totalSubTopic += 1
        totalFrequency += frequency
      }
    }
    //比这个多的就是比较重要的
    const aveFrequency = totalFrequency / totalSubTopic
    let variance = 0
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        const { frequency } = this.slots[topic][subTopic]
        variance += (frequency - aveFrequency) * (frequency - aveFrequency)
      }
    }
    const std = Math.sqrt(variance / totalSubTopic)
    const threshold = aveFrequency + 1.5 * std
    return threshold
  }

  toList():ICustomSlot[] {
    const list:ICustomSlot[] = []
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        list.push({
          topic: topic,
          subTopic: subTopic,
          content: this.slots[topic][subTopic].content,
          frequency: this.slots[topic][subTopic].frequency
        })
      }
    }
    return list
  }

  getTopicAndSubTopic(): Record<string, Array<string>> {
    const res:Record<string, Array<string>> = {}
    for (const topic in this.slots) {
      if (!res[topic]) res[topic] = []
      for (const subTopic in this.slots[topic]) {
        res[topic].push(subTopic)
      }
    }
    return res
  }

  toRecord():Record<string, contentWithFrequency> {
    const res:Record<string, contentWithFrequency> = {}
    for (const topic in this.slots) {
      for (const subTopic in this.slots[topic]) {
        const content = this.slots[topic][subTopic]
        res[`${topic}::${subTopic}`] = {
          content: content.content,
          frequency: content.frequency
        }
      }
    }
    return res
  }

  add(slot: UserSlot) {
    if (!this.slots[slot.topic]) {
      this.slots[slot.topic] = {}
    }
    if (!this.slots[slot.topic][slot.subTopic]) {
      this.slots[slot.topic][slot.subTopic] = { content:slot.content, frequency:slot.frequency }
    } else {
      this.slots[slot.topic][slot.subTopic].content += `。${slot.content}`
    }
  }

  async merge(another: UserSlots, logInfo?: ILogInfo)  {
    const newUserSlot = await _merge(this, another, logInfo)
    for (const topic in another.slots) {
      for (const subTopic in another.slots[topic]) {
        const newMemo = another.slots[topic][subTopic]
        if (!this.slots[topic]) {
          this.slots[topic] = {}
        }
        if (!this.slots[topic][subTopic]) {
          this.slots[topic][subTopic] = newMemo
        }
      }
    }
    for (const { topic, subTopic, content } of newUserSlot) {
      this.slots[topic][subTopic].content = content
      this.slots[topic][subTopic].frequency += 1
    }

    async function _merge(oldMemo:UserSlots, newMemo:UserSlots, logInfo?: ILogInfo):Promise<UserSlot[]> {
      const llm = new LLM({
        meta: {
          ...logInfo,
          promptName: 'MergeSlotArray',
          description: '提取客户画像'
        },
      })
      const promptTemplate = ChatPromptTemplate.fromMessages([SystemMessagePromptTemplate.fromTemplate(getMergePrompt())])
      const memo = formatMemo(oldMemo, newMemo)
      if (memo == '') {
        return []
      }
      const response = await llm.predictMessage(promptTemplate, { memo: memo })
      const slotOriginList = response.split ('\n').map ((item) =>
        UserSlot.fromString (item, logInfo)
      )

      const slotList = (await Promise.all (slotOriginList)).filter ((slot) => slot !== null)
      return slotList as unknown as UserSlot[]
    }
    function formatMemo(oldMemo:UserSlots, newMemo:UserSlots):string {
      const topicSubtopicArray:Record<string, Array<string>> = {}
      for (const topic in oldMemo.slots) {
        for (const subTopic in oldMemo.slots[topic]) {
          if (newMemo.isTopicSubTopicExist(topic, subTopic)) {
            if (!topicSubtopicArray[topic]) topicSubtopicArray[topic] = []
            topicSubtopicArray[topic].push(subTopic)
          }
        }
      }
      let res:string = ''
      for (const topic in topicSubtopicArray) {
        res += `- ${topic}\n`
        for (const subTopic of topicSubtopicArray[topic]) {
          res += ` - ${subTopic}\n`
          res += `  - 旧画像：${oldMemo.slots[topic][subTopic].content}\n`
          res += `  - 新画像：${newMemo.slots[topic][subTopic].content}\n`
        }
      }
      return res
    }

    function getMergePrompt(): string {
      return `# 画像合并
- 你是一个智能画像管理器，负责控制客户画像
- 你将收到关于客户不同主题/方面的画像，关于每个不同的主题/方面，有一条是旧的，一条是新的
- 你应更新旧的画像，以包含新的画像中的信息，并以输出格式返回最终的画像（每个主题/方面5句话以内）

## 替换旧画像
- 如果新画像与旧画像完全冲突，你应该用新的画像替换旧的画像
示例
INPUT
- 基本信息
  - 年龄
    - 旧画像：客户39岁
    - 新画像：客户40岁
  - 居住地
    - 旧画像：北京
    - 新画像：上海
OUTPUT
- 基本信息::年龄::客户39岁，客户40岁
- 基本信息::居住地::北京，上海

## 合并画像
- 如果旧画像中包含新画像中没有的信息，你应该将旧画像和新画像合并
- 你需要总结新旧画像的内容，以便在最终画像中包含充分的信息
示例
INPUT
- 个性
  - 情绪反应
    - 旧画像：下雨天客户有时会哭泣
    - 新画像：下雨天客户会想起了家乡
- 心理特征
  - 价值观
    - 旧画像：坚持学习，活到老学到老，获到老是客户一生的信念
    - 新画像：坚持学习，活到老学到老
OUTPUT
- 个性::情绪反应::下雨天客户会想起家乡，可能是其下雨天哭泣的原因之一
- 心理特征::价值观::坚持学习，活到老学到老是客户的核心价值观，体现了其对终身学习的高度重视

## 规则限制
- 理解画像，你可以从新画像和旧画像中推断信息以决定正确的操作
- 不要返回上面提供的自定义少量提示中的任何内容
- 严格遵守正确的格式
- 保持画像的简洁性

## 客户画像
{memo}`
    }
  }
}


export function chatHistoryWithRoleAndDateListToString(chatHistory:ChatHistoryWithRoleAndDate[]):string {
  return `## 对话记录
${chatHistory.map((item) => {
    return `[${item.date}] ${item.role === 'user' ? '客户' : '老师'}：${item.message.replace('\n', ' ')}`
  }).join('\n')}`
}
export abstract class BaseExtractUserSlots {
  abstract getTopicRecommendations():string
  abstract getTopicRules():string

  private chatHistoryServiceClient:ChatHistoryService
  private chatStateStoreClient:ChatStateStore

  constructor(chatHistoryServiceClient:ChatHistoryService, chatStateStoreClient:ChatStateStore) {
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.chatStateStoreClient = chatStateStoreClient
  }

  async extractUserSlots(chatId: string, round:number = 6, logInfo?: ILogInfo):Promise<UserSlots> {
    let chatHistoryRaw = await this.chatHistoryServiceClient.getRecentConversations(chatId, round, 'user')
    if (chatHistoryRaw.length > 20) {
      chatHistoryRaw = chatHistoryRaw.slice(-20)
    }
    const chatHistory = chatHistoryRaw.map((message) => ({ role:message.role, date:dayjs(message.created_at).format('YYYY/MM/DD HH:mm:ss'), message:message.content }))
    return await this.extractUserSlotsFromChatHistory({
      chatId,
      chatHistory,
      logInfo,
      topicRecommendations: this.getTopicRecommendations(),
      topicRules: this.getTopicRules()
    })
  }

  async extractUserSlotsFromChatHistory({
    chatId,
    chatHistory,
    logInfo,
    topicRecommendations,
    topicRules
  }:{
    chatId:string
    chatHistory: ChatHistoryWithRoleAndDate [],
    logInfo?: ILogInfo,
    topicRecommendations:string,
    topicRules:string
  }):Promise<UserSlots> {
    const chatState = await this.chatStateStoreClient.get(chatId)
    const prevCustomUserSlots = UserSlots.fromRecord(chatState.userSlots ?? {})
    const previousTopicAndSubTopic = prevCustomUserSlots.getTopicAndSubTopic()
    const slotList = await this.extract({ chatHistory, previousTopicAndSubTopic, logInfo, topicRecommendations, topicRules })
    const currentCustomUserSlots = new UserSlots(slotList)

    if (prevCustomUserSlots) {
      await prevCustomUserSlots.merge(currentCustomUserSlots, logInfo)
      await this.persistUserCustomSlot(chatId, prevCustomUserSlots)
      return prevCustomUserSlots
    } else {
      await this.persistUserCustomSlot(chatId, currentCustomUserSlots)
      return currentCustomUserSlots
    }
  }

  async persistUserCustomSlot(chatId: string, customUserSlots:UserSlots) {
    await this.chatStateStoreClient.update(chatId, {
      userSlots:customUserSlots.toRecord()
    })
  }

  public async extract (
    {
      chatHistory,
      previousTopicAndSubTopic,
      logInfo,
      topicRecommendations,
      topicRules
    }:{
      chatHistory: ChatHistoryWithRoleAndDate [],
      previousTopicAndSubTopic: Record<string, Array<string>>,
      logInfo?: ILogInfo,
      topicRecommendations:string,
      topicRules:string
    }
  ): Promise<UserSlot []> {
    const previousTopicAndSubTopicPrompt = this.topicAndSubTopicIntoPrompt(previousTopicAndSubTopic)
    return await this.rawExtract({ chatHistory, previousTopicAndSubTopicPrompt, logInfo, topicRecommendations, topicRules })
  }

  async rawExtract({
    chatHistory,
    previousTopicAndSubTopicPrompt,
    logInfo,
    model = 'gpt-5-mini',
    topicRecommendations,
    topicRules
  }: {chatHistory:ChatHistoryWithRoleAndDate [];
     previousTopicAndSubTopicPrompt: string,
     logInfo?: ILogInfo,
     model?: OpenAIModelName,
     topicRecommendations:string,
     topicRules:string
    }) {
    const llm = new LLM ({
      model: model,
      maxTokens: 5000,
      meta: {
        ...logInfo,
        promptName: 'ExtractSlotArray',
        description: '提取客户槽位'
      },
    })
    const systemPrompt = await this.getExtractPrompt()
    const chatHistoryString = chatHistoryWithRoleAndDateListToString(chatHistory)
    const promptTemplate = ChatPromptTemplate.fromMessages([systemPrompt])

    const preResponse = await llm.predictMessage(promptTemplate, {
      topicRules: topicRules,
      topicRecommendations: topicRecommendations,
      previousTopicAndSubTopicPrompt: previousTopicAndSubTopicPrompt,
      chatHistory: chatHistoryString,
    })
    const llmResponse = XMLHelper.extractContent(preResponse, 'result')
    if (!llmResponse) {
      return []
    }

    if (llmResponse.includes ('NONE')) {
      return []
    }

    const slotOriginList = llmResponse.split ('\n').map ((item) =>
      UserSlot.fromString (item, logInfo)
    )

    const slotList = (await Promise.all (slotOriginList)).filter ((slot) => slot !== null)
    return slotList as unknown as UserSlot[]
  }

  async getExtractPrompt():Promise<SystemMessagePromptTemplate> {
    return SystemMessagePromptTemplate.fromTemplate(`# 画像提取
- 你是一位专业的心理学家，你的责任是仔细阅读客户与其他方的对话。然后提取相关且重要的事实，这些信息将有助于评估客户的状态
- 请参考规则限制，主题规则，主题建议，基于已有主题和对话记录提取/推断相关的事实和偏好，并按输出格式返回

## 规则限制
- 如果客户有提到时间敏感的信息，试图推理出具体的日期
- 当可能时，请使用具体日期，而不是使用“今天”或“昨天”等相对时间
- 如果在以下对话中没有找到任何相关信息，可以返回空列表
- 确保按照格式和示例部分中提到的格式返回响应
- 如果内容中的主体不是客户，应该在主题中标出
- 如果与主题建议不匹配，请生成一个新的主题而不是使用主题建议中的主题
- 相同的内容不需要在不同的 topic 和 sub_topic 下重复，选择最相关的主题和子主题即可
- 相同的 topic 和 sub_topic 只能出现一次
- 不应将assistant说的内容推理为客户的状态
- 客户回复好滴，表情不代表客户同意或积极参与，应忽略
- 忽视客户对对方的称呼：比如客户称呼对方为小姨，因为对其他方的称呼不一定代表真实的关系，不需要推断客户有一个小姨，只需要记录客户称呼其他方为小姨即可
- 请注意，你要准确地提取和推断客户相关的信息，而非老师的
- 你应该检测客户输入的语言，并用相同的语言记录事实。如果在以下对话中没有找到任何相关事实、客户记忆和偏好，你可以返回"NONE"

## 输出格式
- 请先在 <think></think> 标签中结合客户对话，主题建议和已有的主题分析这段对话需要应当输出什么主题和子主题
- 然后在 <result></result> 标签中直接从think和对话中提取事实和偏好，并按顺序列出：
- TOPIC::SUB_TOPIC::MEMO
例如：
- 工作::职称::软件工程师
每行代表一个事实或偏好，包含：
1. TOPIC: 主题，表示该偏好的类别
2. SUB_TOPIC: 详细主题，表示该偏好的具体类别
3. MEMO: 提取的信息、事实或偏好
这些元素应以::分隔，并以 "- " 开头

## 主题规则
{topicRules}

## 主题建议
以下是一些主题和子主题的建议，你需要参考这些主题和子主题来提取信息
如果你认为有必要，可以创建自己的主题/子主题，任何有助于评估客户状态的信息都是受欢迎的
{topicRecommendations}

{previousTopicAndSubTopicPrompt}

{chatHistory}`
    ) }

  topicAndSubTopicIntoPrompt(topicAndSubtopic: Record<string, Array<string>>): string {
    let res = ''
    for (const topic in topicAndSubtopic) {
      res += `- ${topic}：`
      topicAndSubtopic[topic].forEach((value, index) => {
        if (index != 0) {
          res += '，'
        }
        res += value
      })
      res += '\n'
    }
    if (res != '') {
      res = `## 已有主题
如果对话中再次提到相同的主题/子主题，请考虑使用相同的主题/子主题
${res}`
    }
    return res.trim()
  }


}
