export interface IActionInfo {
  guidance: string
  callback?: () => Promise<void>
}

/**
 * 元行为组件
 */
export abstract class MetaActionComponent {
  /**
   * 激活元行为组的条件
   * 注意，重复命名的action，调度逻辑会被后者覆盖
   */
  public abstract activeStatus(chatId: string): Promise<boolean>
  /**
   * 能使用什么 Action
   */
  public abstract getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null>
  /**
   * 使用哪一组 MetaAction
   */
  public abstract getMetaAction(): Promise<Record<string, string>>
  /**
   * 使用什么 ThinkPrompt
   */
  public abstract getThinkPrompt(): Promise<string>
  /**
   * 元行为组激活后的附加信息
   * @param chatId
   */
  public abstract getGuidance(chatId: string): Promise<string>
}
