import logger from 'model/logger/logger'
import { IActionInfo, MetaActionComponent } from './component'

export interface MetaActionStage {
  thinkPrompt: string
  metaActions: string
  guidance: string
}

export class MetaActionRouter {
  private readonly components: MetaActionComponent[]
  private actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {}

  constructor(componentList: MetaActionComponent[]) {
    this.components = componentList
  }

  public async getStageName(chat_id: string): Promise<string> {
    let activeComponent: MetaActionComponent | null = null
    for (const component of this.components) {
      if (await component.activeStatus(chat_id)) {
        activeComponent = component
        break
      }
    }
    return activeComponent?.constructor.name || ''
  }

  private async registerActionList() {
    for (const component of this.components) {
      const actionList = await component.getAction()
      if (actionList) {
        this.actionMap = { ...this.actionMap, ...actionList }
      }
    }
  }

  public async handleAction(chat_id: string, round_id: string, actions: string[]): Promise<IActionInfo> {
    if (Object.keys(this.actionMap).length === 0) { await this.registerActionList() }
    const actionInfo: IActionInfo = { guidance: '' }
    if (!this.actionMap || Object.keys(this.actionMap).length == 0) { return actionInfo }
    for (const action of actions) {
      if (this.actionMap[action]) {
        return await this.actionMap[action](chat_id, round_id)
      }
    }
    return actionInfo
  }

  public async getThinkAndMetaActions(chat_id: string): Promise<MetaActionStage> {
    if (Object.keys(this.actionMap).length === 0) { await this.registerActionList() }
    let activeComponent: MetaActionComponent | null = null
    for (const component of this.components) {
      if (await component.activeStatus(chat_id)) {
        activeComponent = component
        break
      }
    }
    if (!activeComponent) {
      logger.error({ chat_id:chat_id }, '没有可用的元行为组')
      return {
        thinkPrompt: '',
        metaActions: '',
        guidance: '',
      }
    }
    logger.trace({ chat_id: chat_id }, `当前使用的元行为组：${activeComponent.constructor.name}`)
    return {
      thinkPrompt: await activeComponent.getThinkPrompt(),
      metaActions: this.formatMetaAction(await activeComponent.getMetaAction()),
      guidance: await activeComponent.getGuidance(chat_id),
    }
  }

  private formatMetaAction(metaAction:Record<string, string>): string {
    return Object.entries(metaAction).map(([key, value]) => `- ${key}：${value}`).join('\n')
  }
}