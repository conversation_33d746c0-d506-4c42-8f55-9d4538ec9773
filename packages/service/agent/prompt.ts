import * as hub from 'langchain/hub/node'
import { Config } from 'config'
import { Runnable } from '@langchain/core/runnables'

// 设置 LangSmith 环境变量（项目名优先 params，其次配置）
process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
process.env.LANGCHAIN_PROJECT = Config.setting.projectName
process.env.LANGCHAIN_TRACING_V2 = 'true'

// 你所有需要管理的 prompt key
type PromptKey = 'free-think' | 'free-plan' | 'free-route' | 'free-compose'

// 用于存储已拉取的 prompt 实例
const promptCache: { [key in PromptKey]?: Runnable } = {}

export async function getPrompt(name: PromptKey): Promise<Runnable> {
  if (!promptCache[name]) {
    promptCache[name] = await hub.pull(name)
  }
  return promptCache[name]!
}