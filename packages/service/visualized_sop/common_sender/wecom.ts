import { IWecomMsgType } from 'model/juzi/type'
import { HasRepeatedMsg, MessageSender } from '.'
import { WecomMessageSender } from '../../message_handler/juzi/message_sender'
import { MessageText, MessageImage, MessageVideo, MessageAudio, MessageFile, MessageSticker, MessageWecomVoice, MessageWecomCard, MessageWecomVideoChannel, MessageSendOption, MessageYCloudTemplate } from './type'
import { getUserId } from 'config/chat_id'
import logger from 'model/logger/logger'

export class WecomCommonMessageSender extends MessageSender {
  private wecomMessageSender:WecomMessageSender

  constructor(wecomMessageSender:WecomMessageSender, hasRepeatedMsg:HasRepeatedMsg) {
    super(hasRepeatedMsg)
    this.wecomMessageSender = wecomMessageSender
  }

  async sendText(chatId: string, msg: Omit<MessageText, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.text.trim())
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id: chatId,
      user_id:userId,
      ai_msg: msg.text.trim()
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendImage(chatId: string, msg: Omit<MessageImage, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id:chatId,
      user_id:userId,
      ai_msg: msg.description,
      send_msg:{
        type:IWecomMsgType.Image,
        url:msg.url
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendVideo(chatId: string, msg: Omit<MessageVideo, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id:chatId,
      user_id:userId,
      ai_msg: msg.description,
      send_msg: {
        type:IWecomMsgType.Video,
        url:msg.url
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendAudio(chatId: string, msg: Omit<MessageAudio, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendFile(chatId: string, msg: Omit<MessageFile, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id:chatId,
      user_id:userId,
      ai_msg: msg.description,
      send_msg: {
        type:IWecomMsgType.File,
        url:msg.url,
        name:msg.filename
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendSticker(chatId: string, msg: Omit<MessageSticker, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomVoice(chatId: string, msg: Omit<MessageWecomVoice, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id:chatId,
      ai_msg: msg.description,
      user_id:userId,
      send_msg: {
        type:IWecomMsgType.Voice,
        voiceUrl:msg.url,
        duration:msg.duration
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendWecomCard(chatId: string, msg: Omit<MessageWecomCard, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id:chatId,
      ai_msg: msg.description,
      user_id:userId,
      send_msg: {
        type:IWecomMsgType.Link,
        sourceUrl: msg.sourceUrl,
        title: msg.title,
        summary: msg.summary,
        imageUrl: msg.imageUrl
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendWecomVideoChannel(chatId: string, msg: Omit<MessageWecomVideoChannel, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const userId = getUserId(chatId)
    await this.wecomMessageSender.sendById({
      chat_id:chatId,
      user_id:userId,
      ai_msg: msg.description,
      send_msg: {
        type:IWecomMsgType.VideoChannel,
        avatarUrl:msg.avatarUrl,
        coverUrl:msg.coverUrl,
        description:msg.wecomContentdescription,
        feedType:4,
        nickname:msg.nickname,
        thumbUrl:msg.thumbUrl,
        url:msg.url,
        extras:msg.extras
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }

  async sendYCloudTemplate(chatId: string, msg: Omit<MessageYCloudTemplate, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
}