import { commonSleep } from 'lib/schedule/schedule'
import { <PERSON><PERSON>ll, MessageAudio, MessageFile, MessageImage, MessageSendOption, MessageSticker, MessageText, MessageVideo, MessageWecomCard, MessageWecomVideoChannel, MessageWecomVoice, MessageYCloudTemplate, SendMessageType } from './type'

export abstract class MessageSender {
  hasRepeatedMsg:HasRepeatedMsg
  constructor(hasRepeatedMsg:HasRepeatedMsg) {
    this.hasRepeatedMsg = hasRepeatedMsg
  }
  abstract sendText(chatId:string, msg:Omit<MessageText, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendImage(chatId:string, msg:Omit<MessageImage, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendVideo(chatId:string, msg:Omit<MessageVideo, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendAudio(chatId:string, msg:Omit<MessageAudio, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendFile(chatId:string, msg:Omit<MessageFile, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendSticker(chatId:string, msg:Omit<MessageSticker, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendWecomVoice(chatId:string, msg:Omit<MessageWecomVoice, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendWecomCard(chatId:string, msg:Omit<MessageWecomCard, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendWecomVideoChannel(chatId:string, msg:Omit<MessageWecomVideoChannel, 'type'>, opt?:MessageSendOption):Promise<void>
  abstract sendYCloudTemplate(chatId:string, msg:Omit<MessageYCloudTemplate, 'type'>, opt?:MessageSendOption):Promise<void>

  async sendMsg(chatId:string, msg:MessageAll[], opt?:MessageSendOption):Promise<void> {
    for (const singleMsg of msg) {
      if (singleMsg.type == SendMessageType.text) {
        await this.sendText(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.image) {
        await this.sendImage(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.video) {
        await this.sendVideo(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.audio) {
        await this.sendAudio(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.file) {
        await this.sendFile(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.sticker) {
        await this.sendSticker(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.wecomVoice) {
        await this.sendWecomVoice(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.wecomCard) {
        await this.sendWecomCard(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.wecomVideoChannel) {
        await this.sendWecomVideoChannel(chatId, singleMsg, opt)
      } else if (singleMsg.type == SendMessageType.yCloudTemplate) {
        await this.sendYCloudTemplate(chatId, singleMsg, opt)
      }
      await commonSleep()
    }
  }
}

export interface HasRepeatedMsg {
  hasRepeatedMsg(chat_id: string, toMatch: string): Promise<boolean>
}