import type { IReceivedMessage } from 'model/juzi/type'

/** 应用清单（manifest） */
export interface AppManifest {
    projectName: string // 项目名
    agentName: string // AI 扮演的角色的人名
    humanTransferMsgMap: Record<number | string, string>
    extractUserSlots: {
      topicRecommendations: string
      topicRules: string
    }
}

/** override（可插拔的项目定制函数） */
export interface AppOverride<M extends AppManifest = AppManifest> {
    /** 启动完成回调 */
    onInit?: () => Promise<void>

    /** 自定义欢迎语 */
    sendWelcomeMessage?: (chatId: string, userId: string) => Promise<void>

    /** 未知消息 */
    handleUnknownMessage?: (message: IReceivedMessage) =>Promise<void>

    /** 图片消息 */
    handleImageMessage?: (imageUrl: string, chatId: string) => Promise<string>

    /** 视频消息 */
    handleVideoMessage?: (videoUrl: string, chatId: string) => Promise<string>

    /** 自定义事件入口 */
    handleEvent?: (data: any) => Promise<void>
}

export function defineApp<T extends AppManifest>(m: T): T {
  return m
}

export function defineOverride<T extends AppOverride>(o: T): T {
  return o
}