import { LLMReplyBaseParam } from 'service/llm/llm_reply'
import { PathHelper } from 'lib/path'
import { catchError } from 'lib/error/catchError'
import { FileHelper } from 'lib/file'
import logger from 'model/logger/logger'
import { randomSleep } from 'lib/schedule/schedule'
import {
  WhatsappCanSentMessageType,
  WhatsappMessageMedia,
  WhatsappMessageType
} from 'service/message_handler/ycloud/message_sender'
import { llmReplyClient, yCloudMessageSender } from '../service/instance'


export class Reply {

  public static async invoke(param: LLMReplyBaseParam) {
    await llmReplyClient.invoke({
      ...param,
      promptName: param.promptName,
      messageSender: async (line: string,
        chat_id: string,
        user_id: string,
        round_id: string,
        noSplit?: boolean,
        short_description?:string
      ) => {

        // 发送消息
        if (noSplit) {
          // 直接把文件移除掉，进行输出
          line = line.replaceAll(/\[[^\]]*]/g, '')

          return await yCloudMessageSender.sendById({
            chat_id: chat_id,
            ai_msg: line,
            type: 'text',
          }, { round_id: round_id, shortDes: short_description ? `[${short_description}]` : undefined })
        } else {
          // 将文件部分提取出来，放到文本后面。
          const parts = line.split(/\[((?:.*?)_(?:\w{4})\.(?:\w+))\]/)
          const fileRegex =   /.*?_\w{4}\.\w+/

          let textPart = ''
          const filePart: string[] = []

          for (let part of parts) {
            if (fileRegex.test(part)) {
              filePart.push(part)
            } else {
              if (part.endsWith('：') || part.endsWith(':')) {
                part = part.replace(/[：:]/, '')
              }

              textPart += part
            }
          }

          textPart = textPart.replaceAll(/\s+/g, ' ').replaceAll(/\[[^\]]*]/g, '').trim()

          if (textPart) {
            await yCloudMessageSender.sendById ({
              chat_id: chat_id,
              ai_msg: textPart,
              type:'text'
            }, { round_id: round_id })
          }

          for (const fileString of filePart) {
            // 这是一个文件名
            const description = fileString.split('_')[0]
            const extension = PathHelper.getFileExt(fileString)
            const fileName = fileString

            // 构建消息对象
            let message:WhatsappCanSentMessageType | undefined
            let type:WhatsappMessageType

            const fileUrl = `https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/rag_file/${encodeURIComponent(fileName)}`
            const [err, res]  = await catchError(FileHelper.getFileSizeFromUrl(fileUrl))
            if (err || (res && res === 0)) {
              // 文件不存在，有可能是模型幻觉，或者文件被删除了
              logger.error(`${fileName}文件不存在`)
              continue
            }

            // 根据文件后缀名判断文件类型
            switch (extension.toLowerCase ()) {
              case 'jpg':
              case 'jpeg':
              case 'png':
              case 'webp':
                message = <WhatsappMessageMedia>{
                  link: fileUrl,
                  caption:'image'
                }
                type = 'image'
                break
              case 'mp4':
              case 'avi':
              case 'mov':
              case 'wmv':
              case 'flv':
              case 'mkv':
                message = <WhatsappMessageMedia>{
                  link: fileUrl,
                  caption:'video'
                }
                type = 'video'
                break
              default:
                message = <WhatsappMessageMedia>{
                  filename: description,
                  link: fileUrl,
                  caption:'document'
                }
                type = 'document'
            }

            await randomSleep(3000, 5000)

            // 发送文件消息
            await yCloudMessageSender.sendById ({
              chat_id: chat_id,
              ai_msg: `[${description}]`,
              send_msg: message,
              type:type
            }, {
              shortDes: `[${description}]`,
              round_id: round_id
            })
          }
        }

      }
    })
  }
}