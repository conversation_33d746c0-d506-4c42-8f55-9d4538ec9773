import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { isScheduleTimeAfter } from '../../helper/tool/creat_schedule_task'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterCourse1 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    return isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })
  }
  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourse1)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourse1)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}