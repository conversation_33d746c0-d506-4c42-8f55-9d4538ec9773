import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class DuringCourse extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    return await DataService.isWithinClassTime(currentTime)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.duringCourse)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.duringCourse)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}