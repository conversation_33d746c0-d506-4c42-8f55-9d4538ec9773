import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterCourseWeek extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    return Boolean(currentTime.post_course_week) && currentTime.day != 1
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourseWeek)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourseWeek)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}