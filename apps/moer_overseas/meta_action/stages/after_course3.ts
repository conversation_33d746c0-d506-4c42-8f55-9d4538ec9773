import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { isScheduleTimeAfter } from '../../helper/tool/creat_schedule_task'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { PostAction } from './post_action'

export class AfterCourse3 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    return isScheduleTimeAfter(currentTime, { is_course_week: true, day: 3, time: '21:00:00' })
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发起报名邀约': PostAction.sendInvitation,
      '发送学员案例': PostAction.sendCaseImage,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourse3)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourse3)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}