import { EventTracker } from 'model/logger/data_driven'
import { LLMReply } from 'service/llm/llm_reply'
import { MemoryStore } from 'service/memory/memory_store'
import { SendMessageResultHandler } from 'service/message_handler/juzi/send_result_handler'
import { Configuration, WhatsappMessagesApi, YCloudMessageSender } from 'service/message_handler/ycloud/message_sender'
import { RAGHelper } from 'service/rag/rag'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { YCloudCommonMessageSender } from 'service/visualized_sop/common_sender/ycloud'
import { FreeThink } from 'service/agent/freethink'
import { BaseHumanTransfer } from 'service/human_transfer/human_transfer'
import { chatHistoryServiceClient, chatStateStoreClient, chatDBCommonClient } from './base_instance'
import { enterpriseName } from './global_data'
import { PrismaMongoClient as PrismaMongoCommonClient } from 'model/mongodb/prisma'


const ycloudApiKey = 'b129ab33adb30bce3e9c0e77defd8011'

export const eventTrackClient = new EventTracker(PrismaMongoCommonClient.newInstance(enterpriseName))
export const memoryStoreClient = new MemoryStore(chatHistoryServiceClient, chatStateStoreClient)
export const humanTransferClient = new BaseHumanTransfer(chatDBCommonClient, chatStateStoreClient)
export const llmReplyClient = new LLMReply(chatDBCommonClient, chatHistoryServiceClient)
export const sendMessageResultHandlerClient = new SendMessageResultHandler(chatHistoryServiceClient)
export const yCloudClient = new WhatsappMessagesApi(new Configuration({ apiKey: ycloudApiKey }))
export const yCloudMessageSender = new YCloudMessageSender(yCloudClient, chatHistoryServiceClient)
export const ragHelperClient = new RAGHelper(chatHistoryServiceClient)
export const moerOverseasExtractUserSlots = new ExtractUserSlots(chatHistoryServiceClient, chatStateStoreClient)
export const yCloudCommonMessageSender = new YCloudCommonMessageSender(yCloudMessageSender, chatHistoryServiceClient)
export const freeThinkClient = new FreeThink(chatHistoryServiceClient, eventTrackClient)