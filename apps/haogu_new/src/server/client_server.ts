import express from 'express'
import { init } from './bootstrap'
import { catchGlobalError } from 'model/server/server'
import { register, Counter } from 'lib/prometheus_client'
import logger from 'model/logger/logger'
import type { IReceivedMessage, ISendMessageResult } from 'model/juzi/type'
import { override } from '../config/override'
import {
  sendMessageResultHandlerClient
} from '../config/instance'
import { manifest } from '../config/manifest'

async function main() {
  const ctx = await init()

  const receiveMessageCount = new Counter({
    name: 'haogu_message_receive',
    help: 'The times of haogu message receive',
    labelNames:['bot_id']
  })

  const app = express()
  app.use(express.json())

  app.get('/', (req, res) => {
    logger.log('Hello Client, this is Server!')
    res.send('ok')
  })

  app.post('/message', async (req, res) => {
    const msg: IReceivedMessage = req.body
    ctx.messageHandler.handle(msg)

    receiveMessageCount.labels({ bot_id: (manifest.projectName || 'unknown') }).inc(1)
    res.send('ok')
  })

  app.get('/metrics', async (req, res) => {
    res.status(200).set('Content-Type', register.contentType).send(await register.metrics())
  })

  app.post('/event', async (req, res) => {
    override.handleEvent?.(req.body)
    res.send('ok')
  })

  app.post('/sendResult', async (req, res) => {
    const data: ISendMessageResult = req.body
    sendMessageResultHandlerClient.handle(data)
    res.send('ok')
  })

  ctx.messageHandler.startWorker()

  app.listen(ctx.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${ctx.port}`)
  })

  catchGlobalError()
}

main().catch((e) => {
  console.error(e)
})