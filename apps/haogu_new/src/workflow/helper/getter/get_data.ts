import { DateHelper } from 'lib/date/date'

export interface IScheduleTime {
  is_course_day?: boolean // true 表示上课日，false 表示上课前
  post_course_day?: number // 课程结束后的第几天，1 表示第一天，2 表示第二天，依此类推
  day: number // 1-7 表示上课日第几天，负数代表上课前的几天，例如 0 表示上课前1天，-1 表示上课前2天，依此类推
  time: string // 格式为 'HH:MM:SS'，例如 '08:00:00'
}

export class DataService {
  /**
     * 判断当前时间是否在指定课程时间范围内
     * @param chatId 聊天ID，用于获取当前时间
     * @param timeline 时间线类型，可选值：'beforeCourse' | 'afterCourse' | 'afterSales' | 'inCourse'，默认为 'inCourse'
     * @param day 天数，可选参数，默认为当前天
     * @returns 返回布尔值，表示是否在指定时间范围内
     */
  public static async isInCourseTimeLine(chatId: string, timeline: 'beforeCourse' | 'afterCourse' | 'afterSales' | 'inCourse' = 'inCourse', day?: number) {
    return true
  }

  public static isCompletedCourse(chatId: string, courseDay: number) {
    return true
  }
  public static getTodayCourse(chatId: string) {
    return true
  }
  public static getCourseStartTimeByChatId(chatId: string) {
    return Date
  }
  public static getCurrentTime(chatId: string): IScheduleTime {
    return {
      is_course_day: true,
      post_course_day: 0,
      day: 0, //后续会更新
      time: DateHelper.formatDate(new Date(), 'HH:mm:ss'),
    } as IScheduleTime
  }
  public static getCourseLink(chatId: string, courseDay: number) {
    return ''
  }
  public static isPaidSystemCourse(chatId: string) {
    return true
  }
}