import { MetaActionRouter } from 'service/agent/meta_action/router'
import { AfterAdding } from './stages/after_adding'
import { AfterBonding } from './stages/after_bonding'
import { AfterCourse1 } from './stages/after_course1'
import { AfterCourse4 } from './stages/after_course4'
import { AfterCourse6 } from './stages/after_course6'
import { DuringCourse } from './stages/during_course'

const components = [
  new DuringCourse(),
  new AfterCourse6(),
  new AfterCourse4(),
  new AfterCourse1(),
  new AfterBonding(),
  new AfterAdding(),
]  // 必须严格按照流程倒序添加
// freethink with meta-action
export const metaActionRouter = new MetaActionRouter(components)