import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { PostAction } from './post_action'

export class AfterCourse6 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'afterCourse', 6)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '强推陪跑营服务': PostAction.sendCourseIntro,
      // '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '提供定金方案': PostAction.provideDepositPlan,
      '保留名额': PostAction.reaskAnotherDay,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourse6)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourse6)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}