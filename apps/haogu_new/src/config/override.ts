import { defineOverride } from 'service/appkit/types'


/**
 * 对通用层注入函数
 */
export const override =  defineOverride({
  async onInit() {
    // 启动后钩子（可留空）
  },

  // 加好友后发送欢迎语
  async sendWelcomeMessage(chatId, userId) {
  },

  async handleUnknownMessage(message) {

  },

  async handleImageMessage(imageUrl, chatId) {
    return '【普通图片】'
  },

  async handleVideoMessage(videoUrl, chatId) {
    return '【视频】'
  },

  async handleEvent(data) {
  },
})
