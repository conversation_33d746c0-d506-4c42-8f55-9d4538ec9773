import { ChatDB } from 'service/database/chat'
import { PrismaMongoClient } from '../database/prisma'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { ChatHistoryService } from 'service/chat_history/chat_history'
import { EventTracker } from 'model/logger/data_driven'
import { WecomMessageSender } from 'service/message_handler/juzi/message_sender'
import { MemoryStore } from 'service/memory/memory_store'
import { LLMReply } from 'service/llm/llm_reply'
import { SendMessageResultHandler } from 'service/message_handler/juzi/send_result_handler'
import { HaoguVisualizedSopProcessor } from '../visualized_sop/visualized_sop_processor'
import { HaoguVisualizedGroupSopProcessor } from '../visualized_sop/visualized_group_sop_processor'
import { BaseHumanTransfer, HumanTransfer } from 'service/human_transfer/human_transfer'
import { WecomCommonMessageSender } from 'service/visualized_sop/common_sender/wecom'
import { FreeThink } from 'service/agent/freethink'
import { RA<PERSON><PERSON>elper } from 'service/rag/rag'
import { MessageReplyService } from 'service/message_handler/reply/message_reply'
import { Workflow } from '../workflow/workflow'
import { manifest } from './manifest'
import { ExtractUserSlots } from 'service/user_slots/user_slot'
import { Reply } from 'service/agent/reply'

export const chatDBClient = new ChatDB(PrismaMongoClient.getCommonInstance())
export const chatStateStoreClient = new ChatStateStore(chatDBClient)
export const chatHistoryServiceClient = new ChatHistoryService(PrismaMongoClient.getCommonInstance(), chatStateStoreClient)
export const eventTrackClient = new EventTracker(PrismaMongoClient.getCommonInstance())
export const wecomMessageSender = new WecomMessageSender(chatHistoryServiceClient)
export const memoryStoreClient = new MemoryStore(chatHistoryServiceClient, chatStateStoreClient)
export const llmReplyClient = new LLMReply(chatDBClient, chatHistoryServiceClient)
export const replyClient = new Reply(chatDBClient, chatHistoryServiceClient)
export const sendMessageResultHandlerClient = new SendMessageResultHandler(chatHistoryServiceClient)
export const wecomCommonMessageSender = new WecomCommonMessageSender(wecomMessageSender, chatHistoryServiceClient)
export const haoguVisualizedSopProcessor = new HaoguVisualizedSopProcessor(manifest.projectName, chatDBClient, chatHistoryServiceClient, wecomCommonMessageSender)
export const haoguVisualizedGroupSopProcessor = new HaoguVisualizedGroupSopProcessor(manifest.projectName, PrismaMongoClient.getCommonInstance(), wecomMessageSender)
export const humanTransferClient = new BaseHumanTransfer(chatDBClient, chatStateStoreClient)
export const freeThinkClient = new FreeThink(chatHistoryServiceClient, eventTrackClient)
export const ragHelperClient = new RAGHelper(chatHistoryServiceClient)
export const messageReplyServiceClient = new MessageReplyService(Workflow, chatDBClient, chatHistoryServiceClient)
export const humanTransfer = new HumanTransfer({ transferMessage: manifest.humanTransferMsgMap, eventTracker: eventTrackClient, humanTransferClient })
export const extractUserSlots = new ExtractUserSlots({ chatHistoryServiceClient, chatStateStoreClient, topicRecommendations: manifest.extractUserSlots.topicRecommendations, topicRules: manifest.extractUserSlots.topicRules })