import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { PostAction } from './post_action'

export class AfterAdding extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const afterAdding = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1)
    return Promise.resolve(afterAdding)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提供延期方案': PostAction.enterPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterAdding)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterAdding)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}