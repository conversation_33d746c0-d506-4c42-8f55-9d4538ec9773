import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { DataService } from '../../helper/getter/get_data'

export class DuringCourse extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const duringCourse = await DataService.isInCourseTimeLine(chatId, 'inCourse') && !await DataService.isInCourseTimeLine(chatId, 'afterSales')
    return Promise.resolve(duringCourse)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.duringCourse)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.duringCourse)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}