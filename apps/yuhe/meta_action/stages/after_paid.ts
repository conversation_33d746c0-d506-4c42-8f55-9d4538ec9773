import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { DataService } from '../../helper/getter/get_data'

export class AfterPaid extends MetaActionComponent {

  async activeStatus(chatId: string): Promise<boolean> {
    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return Promise.resolve(isPaid)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterPaid)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterPaid)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}