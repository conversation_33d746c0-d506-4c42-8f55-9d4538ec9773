import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'
import logger from 'model/logger/logger'
import { accountToName } from '../config/config'
import { DataService } from '../helper/getter/get_data'
import { eventTrackClient } from '../service/event_track_instance'
import { humanTransferClient } from '../service/human_transfer_instance'

export enum HumanTransferType {
  UnknownMessageType = 1,
  NotBindPhone = 2,
  ProblemSolving = 3,
  FailedToJoinGroup = 4,
  ProcessImage = 5,
  MessageSendFailed = 6,
  ReaskAnotherDay = 7,
  HesitatePayment = 8,
  LogOutNotify = 9,
  ExecutePostpone = 10,
  VoiceOrVideoCall = 11,
  SoftwareIssue = 12,
  RobotDetected = 13,
  PaidCourse = 14,
  ProcessVideo = 24,
  ProcessVideoFailed = 25,
  ExplicitlyPurchases = 26,
  ExecuteRefresherTraining= 27
}

/**
 * 宇和项目转人工处理
 */
export class HumanTransfer {
  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param transferType
   * @param toHuman
   * @param additionalMsg
   */
  public static async transfer(chatId: string, userId: string, transferType: HumanTransferType, toHuman: boolean | 'onlyNotify' = true, additionalMsg?: string) {
    if (userId === 'null') {
      logger.error('[YuHeHumanTransfer] userId is null', transferType)
      return
    }

    if (transferType !== HumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的 日志在上级进行处理，这里不进行重复处理
      eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, transferType) })
    }

    // 拼接要发送的消息
    const ta_id = chatId.split('_')[1]
    const courseNo = await DataService.getCourseNoByChatId(chatId) ?? 2025
    const accountName = accountToName[ta_id] ?? '未知'
    const ip = await DataService.getIpByChatId(chatId)
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'
    const notificationMessages = {
      [HumanTransferType.UnknownMessageType]: '客户发了一个文件',
      [HumanTransferType.ProcessImage]: '客户发了一张【图片】',
      [HumanTransferType.ProcessVideo]: '客户发了一个【视频】',
      [HumanTransferType.ProblemSolving]: '客户遇到问题',
      [HumanTransferType.ProcessVideoFailed]: '客户发了一个【视频】，识别失败',
      [HumanTransferType.NotBindPhone]: '客户手机号绑定失败',
      [HumanTransferType.SoftwareIssue]: '客户软件或者课程链接出问题',
      [HumanTransferType.MessageSendFailed]: '消息发送失败',
      [HumanTransferType.ReaskAnotherDay]: '客户次日被主动提醒支付',
      [HumanTransferType.RobotDetected]: '客户识别到了AI',
      [HumanTransferType.HesitatePayment] : '客户支付犹豫',
      [HumanTransferType.LogOutNotify]: '客户直播掉线',
      [HumanTransferType.PaidCourse]: '[烟花]客户已支付[烟花]',
      [HumanTransferType.FailedToJoinGroup]: '客户拉群失败',
      [HumanTransferType.ExplicitlyPurchases]: '客户弹幕识别出支付意向',
      [HumanTransferType.ExecutePostpone]: '客户已延期',
      [HumanTransferType.ExecuteRefresherTraining]: '客户已复训',
      [HumanTransferType.VoiceOrVideoCall]: '客户发起语音/视频通话'
    }
    const message = `（${ip}${courseNo}${accountName}）${  notificationMessages[transferType] as string  }，${handleType  }${additionalMsg ? `\n${additionalMsg}` : ''}`
    return await humanTransferClient.transfer(chatId, userId, message, toHuman)
  }
}