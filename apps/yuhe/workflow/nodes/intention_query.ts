import { IWorkflowState } from 'service/llm/state'
import { IChattingFlag } from '../../state/user_flags'
import { ContextBuilder } from '../context'
import { trackInvoke, WorkFlowNode } from './base_node'
import { Node } from './types'
import { firstAskIntention } from '../../client/event_handler'
import { DataService } from '../../helper/getter/get_data'
import dayjs from 'dayjs'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../schedule/type'
import { MetaActionRouter } from 'service/agent/meta_action/router'
import { FreeTalk } from '../freetalk'
import { freeThinkClient, replyClient } from '../../service/instance'
import { AfterAdding } from '../../meta_action/stages/after_adding'
import { CoursePostpone } from '../../meta_action/stages/course_postpone'
import { chatStateStoreClient } from '../../service/base_instance'

export class IntentionQueryNode extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState):Promise<Node> {
    const exitIntentionQueryNode = async() => {
      const next = await FreeTalk.invoke(state)

      await firstAskIntention(state.chat_id, state.user_id)

      return next
    }
    // 强规则如果已经在第一天以后了，强行变free_talk
    const currentTime = await DataService.getCurrentTime(state.chat_id)
    const now = dayjs()
    if ((currentTime.day == 1 && now.hour() >= 12) || (currentTime.day > 1)) {
      return await exitIntentionQueryNode()
    }

    // 如果挖需超过7轮就自动退出
    const roundNum = await chatStateStoreClient.getNodeCount(state.chat_id, 'IntentionQueryNode')
    if (roundNum > 6) {
      return await exitIntentionQueryNode()
    }

    const userState = await chatStateStoreClient.getFlags<IChattingFlag>(state.chat_id)
    if (!userState.is_send_first_ask_intention) { // 如果还没发送表单，简单回复
      const context = await ContextBuilder.build({
        state,
        retrievedKnowledge: true,
        talkStrategyPrompt: '和客户友好交流，简单回答问题'
      })

      await replyClient.invoke({
        state,
        context: context,
        promptName: 'intention_query',
      })

    } else {
      const components = [new CoursePostpone(), new AfterAdding()]
      const metaActionRouter = new MetaActionRouter(components)
      const metaActionStage = await metaActionRouter.getThinkAndMetaActions(state.chat_id)
      const contextComponent = new ContextBuilder({ state })
      const { action, strategy } = await freeThinkClient.invoke(
        state,
        metaActionStage.thinkPrompt,
        metaActionStage.metaActions,
        await contextComponent.customerBehavior(state.chat_id),
        await contextComponent.customerPortrait(state.chat_id),
        await contextComponent.temporalInformation(state.chat_id),
      )

      const actionInfo = await metaActionRouter.handleAction(state.chat_id, state.round_id, action)
      const talkStrategyPrompt = [
        '无论客户最后说什么，参考上述信息回答完客户后，都要严格执行下面的策略，要求极度精简，点到为止',
        strategy,
        metaActionStage.guidance,
        actionInfo.guidance
      ].filter(Boolean).join('\n')

      const context = await ContextBuilder.build({
        state,
        retrievedKnowledge: true,
        customerMemory: true,
        talkStrategyPrompt: talkStrategyPrompt
      })

      await replyClient.invoke({
        state,
        context: context,
        temperature: 0.8,
        maxTokens: 400,
        promptName: 'free_talk',
        postReplyCallBack: actionInfo.callback
      })

      if (action.includes('通用诊断')) {
        // 先调度催促发送抖音截图任务
        await SilentReAsk.schedule(
          TaskName.urge_douyin_screenshot,
          state.chat_id,
          1000 * 60 * 5,
          undefined,
          { auto_retry: true, independent:true }
        )

        return Node.FreeTalk
      }

      if ((await chatStateStoreClient.getFlags<IChattingFlag>(state.chat_id)).is_in_postpone) {
        return Node.FreeTalk
      }

    }
    await firstAskIntention(state.chat_id, state.user_id)

    return Node.IntentionQuery
  }
}