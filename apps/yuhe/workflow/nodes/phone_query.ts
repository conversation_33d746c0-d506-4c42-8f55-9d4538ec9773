import { RegexHelper } from 'lib/regex/regex'
import logger from 'model/logger/logger'
import { IWorkflowState } from 'service/llm/state'
import { trackInvoke, WorkFlowNode } from './base_node'
import { Node } from './types'
import { DataService } from '../../helper/getter/get_data'
import { startAskIntention } from '../../client/event_handler'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { ContextBuilder } from '../context'
import { replyClient } from '../../service/instance'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'

export class PhoneQueryNode extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 查询电话号码
    const phone = await DataService.bindPhoneFromRemark(state.chat_id)
    // 解析回复中客户的手机号
    const history = (await chatHistoryServiceClient.getRecentConversations(state.chat_id, 3, 'user')).filter((item) => item.role == 'user')
    const phoneNumber = RegexHelper.extractPhoneNumber(`${history.map((item) => item.content).join(' ')} ${state.userMessage}`)
    if (phone || phoneNumber) {
      if (phoneNumber) {
        await DataService.bindPhone(state.chat_id, phoneNumber)
      }
      await startAskIntention(state.chat_id, state.user_id)
      return Node.IntentionQuery
    } else {
      const context = await ContextBuilder.build({
        state,
        retrievedKnowledge: true,
        talkStrategyPrompt:`客户当前没有提供注册手机号或提供的手机号校验后不正确，需要重新询问客户购买课程的手机号，有手机号才能匹配到对应的课程并提供后续服务。
特别注意当前不知道且没有途径获取到客户的手机号，只能由客户告知, 所以必须询问客户手机号是多少，即使客户有提到"这个"之类
例如：“咱这边当时买课的手机号是多少？”“咱这边手机号没有在课程中查到，能再确认下手机号么”`
      })

      await replyClient.invoke({
        state,
        promptName: 'phone_query',
        context: context
      })

      const nodeInvokeCount = await chatStateStoreClient.getNodeCount(state.chat_id, 'PhoneQueryNode')

      if (nodeInvokeCount >= 3) {
        logger.debug({ chat_id: state.chat_id }, '没有问出手机号， 转人工')
        await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.NotBindPhone, 'onlyNotify')
        return Node.PhoneQuery
      } else {
        return Node.PhoneQuery
      }
    }
  }
}