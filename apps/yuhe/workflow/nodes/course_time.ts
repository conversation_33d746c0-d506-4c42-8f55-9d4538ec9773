import { ContextBuilder } from '../context'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../../helper/getter/get_data'
import { IWorkflowState } from 'service/llm/state'
import { Node } from './types'
import { replyClient } from '../../service/instance'
import { trackInvoke, WorkFlowNode } from './base_node'

interface courseSchedule{
  day: number
  name: string
  duration: string
  content: string
}

export class CourseTimeNode extends WorkFlowNode {

  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const talkStrategyPrompt = `请结合以下信息回答客户的问题${await this.getCurrentSituation(state.chat_id)}`

    const context = await ContextBuilder.build({
      state,
      temporalInformation: false,
      customerPortrait: false,
      customerChatRounds: 1,
      talkStrategyPrompt: talkStrategyPrompt,
      debug: true
    })

    await replyClient.invoke({
      state,
      promptName: 'course_time',
      context: context,
    })

    return Node.FreeTalk
  }


  private static async getCurrentSituation(chat_id: string) {
    const courseStartTime = await DataService.getCourseStartTimeByChatId(chat_id)
    const currentTime = await DataService.getCurrentTime(chat_id)
    const currentDate = new Date()

    const peipaoCourseStartTime = DateHelper.add(new Date(), 14, 'day')

    const isInCourse = currentTime.is_course_day ? '当前在上课期' : '当前不在上课期'

    if (currentTime.post_course_day) {
      return this.getAfterCoursePrompt()
    }

    let currentSituation = `当前情况:
- 现在时间是：${currentDate.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
`

    if (currentTime.is_course_day) {
      currentSituation += `- 当前是上课期,第${currentTime.day}天`
    } else if (currentTime.day <= 0) {
      currentSituation += `- 课程还未开始，课程开始时间是${courseStartTime.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`
    }

    return `${currentSituation}\n${this.getCourseSchedule()}`
  }

  private static getCourseSchedule() {
    const courseSchedule: courseSchedule[] = [
      {
        day: 1,
        name: '第一节课',
        duration: '18:50',
        content: '账号定位：你会教客户如何去定位自己的账号，包括该通过什么样的方式来把产品推广出去，还有如何去搭建一个自己的账号'
      },
      {
        day: 2,
        name: '第二节课',
        duration: '18:50',
        content: '爆款短视频：你会教客户如何通过AI数字人来拍出爆款的短视频，以及给到客户大量的文案和模板。按照模版拍摄即可'
      },
      {
        day: 3,
        name: '第三节课',
        duration: '18:50',
        content: '矩阵系统：你会教客户如何搭建自己的一个矩阵系统，以及客户的直播该如何去做，该怎么去搭建直播间  还有无人直播该如何去做'
      },
      {
        day: 4,
        name: '第四节课',
        duration: '18:50',
        content: '同城引流：你会教客户如何做抖音的本地生活，帮助客户在同城引流'
      }
    ]

    return `课程信息：
${courseSchedule.map((course) =>
    `- 第${course.day}天 ${course.name}, 上课时间:${course.duration}, 上课内容: ${course.content}`
  ).join('\n')}`
  }

  private static getAfterCoursePrompt() {
    return `当前情况：
系统班已经结束，接下来引导客户加入VIP专属陪跑课
陪跑课背景信息:
`
  }

}