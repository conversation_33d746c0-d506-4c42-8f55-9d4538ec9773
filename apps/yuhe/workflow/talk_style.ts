import { ContextBuilder } from './context'
import { IWorkflowState } from 'service/llm/state'
import { Runnable } from '@langchain/core/runnables'
import { LLM } from 'lib/ai/llm/llm_model'
import { XMLHelper } from 'lib/xml/xml'
import { IEventType } from 'model/logger/data_driven'
import * as hub from 'langchain/hub/node'
import logger from 'model/logger/logger'
import { chatHistoryServiceClient } from '../service/base_instance'
import { eventTrackClient } from '../service/event_track_instance'

export class TalkStyle {
  private static _talkStylePrompt: Runnable | null = null

  private static async getTalkStylePrompt(): Promise<Runnable> {
    if (!this._talkStylePrompt) {
      this._talkStylePrompt = await hub.pull('talk-style')
    }
    return this._talkStylePrompt
  }

  public static async invoke(state: IWorkflowState) {
    const customerPortrait = await new ContextBuilder({ state }).customerPortrait(state.chat_id)
    const chatHistory = await chatHistoryServiceClient.getChatHistory(state.chat_id, 10, 18)
    const talkStylePrompt = await this.getTalkStylePrompt()

    await state.interruptHandler.interruptCheck()
    let result = await LLM.predict(
      talkStylePrompt, {
        temperature: 0.8,
        meta: {
          promptName: 'talk_style',
          chat_id: state.chat_id,
          round_id: state.round_id
        }
      }, {
        customerInfo: customerPortrait,
        chatHistory: chatHistory
      })

    if (!result.includes('</style>')) {
      result += '</style>'
    }
    const customerStyle = XMLHelper.extractContent(result, 'customer') || ''
    const style = XMLHelper.extractContent(result, 'style') || ''

    eventTrackClient.track(state.chat_id, IEventType.TalkStyle, { round_id: state.round_id, customerStyle: customerStyle, style: style })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `customerStyle:${customerStyle}\nstyle:${style}`)
    return { customerStyle, style }
  }
}