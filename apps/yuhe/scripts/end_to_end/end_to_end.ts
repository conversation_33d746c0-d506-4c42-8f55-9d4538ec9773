import { IDBBaseMessage } from 'service/chat_history/chat_history'
import { FileHelper } from 'lib/file'
import path from 'path'
import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'
import { getChatId, getUserId } from 'config/chat_id'
import { JuziMessageHandler } from 'service/message_handler/juzi/message_handler'
import { handleImageMessage, handleUnknownMessage, handleVideoMessage } from '../../client/message_handler'
import { IReceivedMessage, IReceivedMessageSource, IWecomReceivedMsgType } from 'model/juzi/type'
import { UUID } from 'lib/uuid/uuid'
import { faker } from '@faker-js/faker'
import { sleep } from 'lib/schedule/schedule'
import { when } from 'jest-when'
import os from 'node:os'
import dayjs from 'dayjs'
import { Workflow } from '../../workflow/workflow'
import { sendYuHeWelComeMessage } from '../../client/event_handler'
import { DataService } from '../../helper/getter/get_data'
import { Node } from '../../workflow/nodes/types'
import {
  memoryStoreClient
} from '../../service/instance'
import { PrismaMongoClient } from '../../database/prisma'
import { messageReplyServiceClient } from '../../service/message_replay_service_instance'
import { chatHistoryServiceClient, chatDBClient, chatStateStoreClient } from '../../service/base_instance'
import { eventTrackClient } from '../../service/event_track_instance'

export interface PullMessagesOptions {
  specifyChatId?:string[]
  courseNos: number[]
  minUserMessageCount?: number
  maxUserMessageCount?: number
  outputFilePath?: string
}

/**
 * 拉取线上聊天记录，整理为 JSON 格式
 * @param options - 拉取配置选项
 */
export async function pullMessagesFromChatHistory(options: PullMessagesOptions) {
  const { specifyChatId = [], courseNos, minUserMessageCount = 10, maxUserMessageCount = 50, outputFilePath } = options
  const userChatHistory: IDBBaseMessage[][] = []

  console.log(`开始拉取聊天记录，课程编号: ${courseNos.join(', ')}`)
  for (const chatId of specifyChatId) {
    const userMessageCount = await chatHistoryServiceClient.getUserMessageCount(chatId)

    if (userMessageCount >= minUserMessageCount && userMessageCount <= maxUserMessageCount) {
      const messages = await chatHistoryServiceClient.getChatHistoryByChatId(chatId, true)
      userChatHistory.push(messages)
      console.log(`添加聊天记录: ${chatId}，客户消息数: ${userMessageCount}`)
    }
  }

  for (const courseNo of courseNos) {
    const chats = await DataService.getChatsByCourseNo(courseNo)
    console.log(`课程 ${courseNo} 共有 ${chats.length} 个聊天`)

    for (const chat of chats) {
      const chatId = chat.id
      const userMessageCount = await chatHistoryServiceClient.getUserMessageCount(chatId)

      if (userMessageCount >= minUserMessageCount && userMessageCount <= maxUserMessageCount) {
        const messages = await chatHistoryServiceClient.getChatHistoryByChatId(chatId, true)
        userChatHistory.push(messages)
        console.log(`添加聊天记录: ${chatId}，客户消息数: ${userMessageCount}`)
      }
    }
  }

  const finalOutputPath = outputFilePath || path.join(__dirname, 'chat_history.json')
  await FileHelper.writeFile(finalOutputPath, JSON.stringify(userChatHistory, null, 4))

  console.log(`成功拉取 ${userChatHistory.length} 个聊天记录，保存到: ${finalOutputPath}`)
  return userChatHistory
}

/**
 * 读取 JSON 格式聊天记录
 * @param filePath - 文件路径，默认为 chat_history.json
 */
export async function readMessagesFromJSON(filePath?: string) {
  const inputPath = filePath || path.join(__dirname, 'chat_history.json')
  const userChatHistory = await FileHelper.readFile(inputPath)

  const histories = JSON.parse(userChatHistory)

  histories.forEach((h: any) => {
    h.forEach((m: any) => {
      m.created_at = new Date(m.created_at)
    })
  })

  console.log(`成功读取 ${histories.length} 个聊天记录`)
  return histories
}

const messageHandler = new JuziMessageHandler({
  handleUnknownMessage,
  handleImageMessage,
  handleVideoMessage,
  sendWelcomeMessage:sendYuHeWelComeMessage
},
chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient)

export async function initializeMockEnvironment() {
  Config.setting.wechatConfig = await loadConfigByAccountName('yuhe_test')
  Config.setting.startTime = Date.now()
  Config.setting.AGENT_NAME = '大麦老师'
  Config.setting.projectName = 'yuhe'

  messageHandler.startWorker()
}

export function getWxNamePrefix() {
  const machineId = os.hostname() || process.pid.toString()
  return `local${dayjs().format('YYYYMMDD')}-${machineId}`
}

export async function processChatHistory(
  chatHistories: IDBBaseMessage[],
  getCurrentTimeMock: jest.SpyInstance,
  index: number
) {
  // 直接模拟线上客户
  const user_id = getUserId(chatHistories[0].chat_id)
  const chat_id = getChatId(user_id)

  // 清理聊天环境
  await chatHistoryServiceClient.clearChatHistory(chat_id, false)
  await PrismaMongoClient.getInstance().log_store.deleteMany({
    where: {
      chat_id: chat_id
    }
  })

  // 初始化聊天状态
  await chatStateStoreClient.get(chat_id)
  await chatStateStoreClient.clear(chat_id)
  await chatStateStoreClient.update(chat_id, { nextStage:Node.IntentionQuery })
  await chatDBClient.setHumanInvolvement(chat_id, false)
  await memoryStoreClient.clearMemory(chat_id)

  const chat = await chatDBClient.getById(chatHistories[0].chat_id)

  if (chat) {
    // 微信名更新一下
    const wxName = `${getWxNamePrefix()}-${chat.contact.wx_name}`
    await chatDBClient.updateContact(chat_id, user_id, wxName)
    console.log(`开始处理第 ${index + 1} 个聊天记录， ${chat.contact.wx_name} 消息数量: ${chatHistories.filter((item) => item.role === 'user').length}`)
  }

  let lastSendTime: number = new Date().getTime()
  let lastUserMessage = ''

  // 找到第一条客户消息，设置之前的聊天历史
  for (const [messageIndex, message] of chatHistories.entries()) {
    if (message.role === 'user') {
      const preMessages = chatHistories.slice(0, messageIndex)
      if (!preMessages.length) break
      await chatHistoryServiceClient.setChatHistory(
        chat_id,
        preMessages.map((msg) => ({ role: msg.role, content: msg.content }))
      )
      lastSendTime = message.created_at.getTime()
      break
    }
  }

  let isFirstMessage = true

  // 处理每条消息
  for (const [messageIndex, message] of chatHistories.entries()) {
    // 跳过助手消息
    if (message.role === 'assistant') continue

    // 模拟客户时间
    const currentTime = await DataService.getCurrentTimeByDate(message.chat_id, message.created_at)
    when(getCurrentTimeMock).calledWith(chat_id).mockReturnValue(currentTime)

    // 计算并执行延迟
    await executeMessageDelay(lastSendTime, message.created_at.getTime())
    lastSendTime = message.created_at.getTime()

    // 如果上条消息还未被回复，再多等待 5 秒
    const lastMessage = await chatHistoryServiceClient.getLastMessage(chat_id)
    if (lastMessage && lastMessage.content === lastUserMessage) {
      await sleep(5 * 1000)
    }

    // 处理 SOP 消息（在等待之后）
    await handleSOPMessages(chat_id, chatHistories, messageIndex, isFirstMessage)

    // 发送消息
    messageHandler.handle(chatMessageToWecomMessage(message))
    lastUserMessage = message.content

    isFirstMessage = false
  }

  console.log(`第 ${index + 1} 个聊天记录处理完成`)

  // 等待一段时间确保所有消息处理完成
  await sleep(1.5 * 60 * 1000)
}

/**
 * 执行消息延迟
 */
async function executeMessageDelay(lastSendTime: number, currentMessageTime: number): Promise<void> {
  if (lastSendTime === currentMessageTime) {
    // 第一条消息，立即发送
    return
  }

  const diff = currentMessageTime - lastSendTime
  const minInterval = 15000 // 15秒
  const defaultInterval = 40000 // 40秒

  if (diff < minInterval) {
    console.log(`等待 ${diff / 1000} 秒...`)
    await sleep(diff)
  } else {
    console.log(`等待 ${defaultInterval / 1000} 秒...`)
    await sleep(defaultInterval)
  }
}

/**
 * 处理SOP消息
 */
async function handleSOPMessages(
  chatId: string,
  messages: IDBBaseMessage[],
  index: number,
  isFirstMessage: boolean
): Promise<void> {
  if (isFirstMessage) return

  if (index - 1 >= 0 && messages[index - 1].short_description) {
    // 往前一直找到不是 SOP 的消息
    for (let i = index - 1; i >= 0; i--) {
      if (!messages[i].short_description) {
        const preMessages = messages.slice(i + 1, index)
        for (const preMessage of preMessages) {
          await chatHistoryServiceClient.addBotMessage(
            chatId,
            preMessage.content,
            preMessage.short_description
          )
        }
        break
      }
    }
  } else if (
    index - 1 >= 0 &&
    messages[index - 1].role === 'assistant' &&
    messages[index - 1].content.includes('http')
  ) {
    await chatHistoryServiceClient.addBotMessage(
      chatId,
      messages[index - 1].content,
      messages[index - 1].short_description
    )
  }
}

export function chatMessageToWecomMessage(message: IDBBaseMessage): IReceivedMessage {
  return {
    avatar: faker.image.url(),
    botId: UUID.short(),
    botUserId: Config.setting.wechatConfig!.botUserId,
    chatId: UUID.short(),
    contactName: faker.person.firstName(),
    contactType: 0,
    coworker: false,
    externalUserId: UUID.short(),
    imBotId: Config.setting.wechatConfig!.id,
    imContactId: getUserId(message.chat_id),
    token: 'e0d70927040a4efa92b79b7279ecb1c1',
    timestamp: new Date().getTime(),
    isSelf: false,
    messageId: UUID.short(),
    messageType: IWecomReceivedMsgType.Text,
    orgId: '661ce5985ed60835eb133f49',
    source: IReceivedMessageSource.MobilePush,
    payload: {
      text: message.content
    }
  }
}