{"name": "yuhe", "private": true, "scripts": {"tsc-check": "tsc --noEmit", "prisma_generate": "echo 'yuhe uses shared prisma client from model package'", "check-import": "ts-node ../../scripts/check_dependecies.ts", "yuhe:test": "export NODE_ENV=dev WECHAT_NAME=yuhe_test && ts-node client/client_server.ts", "yuhe:online-test": "export NODE_ENV=dev WECHAT_NAME=yuhe_online_test && ts-node client/client_server.ts", "client": "ts-node client/client_server.ts", "yuhe:event:server": "ts-node server/event_server.ts", "yuhe:deploy": "ts-node docker/deploy.ts"}, "devDependencies": {"@types/express": "^4.17.19", "@types/inquirer": "^9.0.8", "@types/jest-when": "^3.5.5", "@types/node": "^20.7.0", "inquirer": "^8.2.6", "jest": "^29.7.0", "jest-when": "^3.7.0", "js-yaml": "^4.1.0", "prisma": "^6.13.0", "ts-node": "^10.9.1", "typescript": "5.8.2"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@langchain/core": "^0.3.70", "@prisma/client": "^6.13.0", "@types/js-yaml": "^4.0.9", "axios": "^1.5.1", "bullmq": "^5.12.9", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "config": "workspace:*", "dayjs": "^1.11.13", "express": "^4.18.2", "langchain": "^0.3.21", "lib": "workspace:*", "lru-cache": "^10.2.0", "model": "workspace:*", "openai": "^4.98.0", "p-limit": "3.1.0", "service": "workspace:*", "zod": "^3.23.8"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}}