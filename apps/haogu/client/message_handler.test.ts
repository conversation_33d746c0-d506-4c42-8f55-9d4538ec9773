/**
 * handleTextAndImageMessage函数测试
 */

import { handleTextAndImageMessage } from './message_handler'
import { ImageAndText } from 'service/message_handler/juzi/message_handler'

// Mock dependencies
jest.mock('lib/ai/llm/llm_model', () => ({
  LLM: jest.fn().mockImplementation(() => ({
    predictMessage: jest.fn().mockResolvedValue('这是一个股票K线图分析：当前股价处于上升趋势，建议关注支撑位在50元附近。'),
    predict: jest.fn().mockResolvedValue('基于文字描述的股票分析结果。')
  }))
}))

jest.mock('@langchain/core/messages', () => ({
  HumanMessage: jest.fn().mockImplementation((content) => content),
  SystemMessage: jest.fn().mockImplementation((content) => content)
}))

jest.mock('model/logger/logger', () => ({
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}))

jest.mock('../service/instance', () => ({
  eventTrackClient: {
    track: jest.fn()
  }
}))

describe('handleTextAndImageMessage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('应该处理包含文本和图片的消息', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: '请帮我分析一下这只股票的走势',
        imageUrl: 'https://example.com/stock-chart.jpg'
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-001', 'user-001')

    expect(result).toBe('这是一个股票K线图分析：当前股价处于上升趋势，建议关注支撑位在50元附近。')
    expect(require('lib/ai/llm/llm_model').LLM).toHaveBeenCalledWith({
      model: 'gpt-5',
      temperature: 0.1,
      maxTokens: 1500,
      meta: {
        promptName: 'haogu_stock_multimodal_analysis',
        chat_id: 'chat-001',
        description: '股票多模态技术分析'
      }
    })
  })

  it('应该处理只有文本的消息', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: '请分析一下茅台股票的投资价值',
        imageUrl: undefined
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-002', 'user-002')

    expect(result).toBe('这是一个股票K线图分析：当前股价处于上升趋势，建议关注支撑位在50元附近。')
  })

  it('应该处理只有图片的消息', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: undefined,
        imageUrl: 'https://example.com/stock-chart.jpg'
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-003', 'user-003')

    expect(result).toBe('这是一个股票K线图分析：当前股价处于上升趋势，建议关注支撑位在50元附近。')
  })

  it('应该处理多条混合消息', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: '这是第一条文本消息',
        imageUrl: undefined
      },
      {
        text: undefined,
        imageUrl: 'https://example.com/chart1.jpg'
      },
      {
        text: '这是第二条文本消息',
        imageUrl: 'https://example.com/chart2.jpg'
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-004', 'user-004')

    expect(result).toBe('这是一个股票K线图分析：当前股价处于上升趋势，建议关注支撑位在50元附近。')
  })

  it('应该处理空消息数组', async () => {
    const imageAndText: ImageAndText[] = []

    const result = await handleTextAndImageMessage(imageAndText, 'chat-005', 'user-005')

    expect(result).toBe('')
  })

  it('应该处理无效消息', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: '',
        imageUrl: undefined
      },
      {
        text: undefined,
        imageUrl: undefined
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-006', 'user-006')

    expect(result).toBe('')
  })

  it('应该在GPT-5失败时降级到GPT-4', async () => {
    // Mock GPT-5失败
    const mockLLM = require('lib/ai/llm/llm_model').LLM
    mockLLM.mockImplementationOnce(() => ({
      predictMessage: jest.fn().mockRejectedValue(new Error('GPT-5调用失败'))
    }))
    
    // Mock GPT-4成功
    mockLLM.mockImplementationOnce(() => ({
      predict: jest.fn().mockResolvedValue('基于文字描述的股票分析结果。')
    }))

    const imageAndText: ImageAndText[] = [
      {
        text: '请分析股票走势',
        imageUrl: 'https://example.com/chart.jpg'
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-007', 'user-007')

    expect(result).toBe('基于文字描述的股票分析结果。')
  })

  it('应该在所有处理都失败时返回空字符串', async () => {
    // Mock所有LLM调用都失败
    const mockLLM = require('lib/ai/llm/llm_model').LLM
    mockLLM.mockImplementation(() => ({
      predictMessage: jest.fn().mockRejectedValue(new Error('GPT-5调用失败')),
      predict: jest.fn().mockRejectedValue(new Error('GPT-4调用失败'))
    }))

    const imageAndText: ImageAndText[] = [
      {
        text: '请分析股票走势',
        imageUrl: 'https://example.com/chart.jpg'
      }
    ]

    const result = await handleTextAndImageMessage(imageAndText, 'chat-008', 'user-008')

    expect(result).toBe('')
  })

  it('应该正确构建多模态内容', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: '请分析这个图表',
        imageUrl: 'https://example.com/chart.jpg'
      }
    ]

    await handleTextAndImageMessage(imageAndText, 'chat-009', 'user-009')

    const mockLLM = require('lib/ai/llm/llm_model').LLM
    const llmInstance = mockLLM.mock.results[0].value
    const predictMessageCall = llmInstance.predictMessage.mock.calls[0]
    
    // 验证传递给LLM的消息结构
    expect(predictMessageCall[0]).toHaveLength(2) // SystemMessage + HumanMessage
    
    const humanMessage = predictMessageCall[0][1]
    expect(humanMessage.content).toEqual([
      { type: 'text', text: '请分析这个图表' },
      { type: 'image_url', image_url: { url: 'https://example.com/chart.jpg' } }
    ])
  })
})

/**
 * 集成测试示例
 */
describe('handleTextAndImageMessage集成测试', () => {
  it('应该生成符合股票分析要求的系统提示词', async () => {
    const imageAndText: ImageAndText[] = [
      {
        text: '分析这个K线图',
        imageUrl: 'https://example.com/kline.jpg'
      }
    ]

    await handleTextAndImageMessage(imageAndText, 'chat-010', 'user-010')

    const mockLLM = require('lib/ai/llm/llm_model').LLM
    const llmInstance = mockLLM.mock.results[0].value
    const predictMessageCall = llmInstance.predictMessage.mock.calls[0]
    
    const systemMessage = predictMessageCall[0][0]
    expect(systemMessage).toContain('专业的股票分析师')
    expect(systemMessage).toContain('K线形态和趋势')
    expect(systemMessage).toContain('技术指标')
    expect(systemMessage).toContain('筹码分布')
    expect(systemMessage).toContain('支撑位和阻力位')
  })
})
