import { getChatId, getUserId } from 'config/chat_id'
import { LLM } from 'lib/ai/llm/llm_model'
import { HumanTransfer, HumanTransferType } from '../human_transfer/human_transfer'
import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'
import { IReceivedMessage, IWecomReceivedMsgType } from 'model/juzi/type'
import logger from 'model/logger/logger'
import { Config } from 'config'
import { AliyunCredentials } from 'lib/cer'
import OpenAI from 'openai'
import axios from 'axios'
import { IChattingFlag } from '../state/user_flags'
import { sendHaoguWelComeMessage } from './event_handler'
import { chatStateStoreClient } from '../service/base_instance'
import { eventTrackClient } from '../service/send_message_instance'
import { ImageAndText } from 'service/message_handler/juzi/message_handler'
import { HumanMessage, SystemMessage } from '@langchain/core/messages'

const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB
const MAX_VIDEO_DUATION = 40 // 秒

// Initialize Aliyun credentials
AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})

export async function handleImageMessage(imageUrl: string, chatId: string) {
  const response = await new LLM({
    temperature: 0,
    maxTokens: 200,
    meta: { promptName: 'image_caption', chat_id: chatId, description: '图片转文本' }
  }).imageChat(imageUrl, `# 图片描述
你是一名导师，正与客户聊天。客户发来一张图片，请先分类图片，然后用一段话解释图片内容

## 分类规则
1. 所有情况均视为普通图片

## 输出格式
- 若为普通图片，文本以“【普通图片】”开头，然后正常描述图片内容，不需要明确声明这不是社交媒体首页截图`)

  eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
    image_url: imageUrl, msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Image) })
  return `${response}`
}

export async function handleTextAndImageMessage(imageAndText: ImageAndText[], chatId: string, userId: string): Promise<string> {
  try {
    logger.debug('开始处理多模态消息', { chatId, userId, messageCount: imageAndText.length })
    // 过滤
    const validMessages = imageAndText.filter((item) => item.text || item.imageUrl)

    if (validMessages.length === 0) {
      logger.warn('没有有效的消息内容', { chatId, userId })
      return ''
    }

    const multiModalQA: Array<{ text?: string, imageUrl?: string }> = []

    for (const item of validMessages) {
      if (item.text && item.text.trim().length > 0) {
        // 添加文本内容
        multiModalQA.push({
          text: item.text.trim()
        })
      }

      if (item.imageUrl) {
        // 添加图片内容
        multiModalQA.push({
          imageUrl: item.imageUrl
        })
      }
    }

    if (multiModalQA.length === 0) {
      logger.warn('没有有效的多模态内容', { chatId, userId })
      return ''
    }

    // 构建专业的股票分析系统提示词
    const systemPrompt = `你是一名专业的股票分析师，擅长分析K线图、筹码分布图、技术指标图等各种股市技术图表。

用户会根据一张图片提出问题。如果用户提供的图片是股市图，请仔细分析图片中的股票技术信息，包括但不限于：
- K线形态和趋势分析（上涨、下跌、震荡等）
- 成交量变化情况
- 技术指标分析（如MACD、RSI、KDJ、布林带等）
- 筹码分布情况和密集区域
- 重要的支撑位和阻力位
- 均线系统分析
- 价格形态识别（如头肩顶、双底等）

请根据图片内容和用户的问题，提供专业、准确的回答。如果用户只发送了图片没有具体问题，请主动分析图片中的关键技术信息`

    // 使用GPT-5进行多模态分析
    const llm = new LLM({
      model: 'gpt-5',
      temperature: 0.2, // 低温确保分析的一致性
      maxTokens: 1500,
      meta: {
        promptName: 'haogu_stock_multimodal_analysis',
        chat_id: chatId,
        description: '股票多模态技术分析'
      }
    })

    // 构建消息
    const humanMessage = new HumanMessage({
      content: multiModalQA
    })

    const messages = [
      new SystemMessage(systemPrompt),
      humanMessage
    ]

    logger.debug('调用GPT-5进行多模态分析', {
      chatId,
      userId
    })

    const analysisResult = await llm.predictMessage(messages)

    if (!analysisResult || analysisResult.trim().length === 0) {
      logger.error('GPT-5返回空结果', { chatId, userId })
      throw new Error('分析结果为空')
    }

    logger.log('GPT-5多模态分析完成', {
      chatId,
      userId,
      responseLength: analysisResult.length
    })

    eventTrackClient.track(chatId, '股票多模态分析', {
      message_count: validMessages.length,
      response_length: analysisResult.length,
      model: 'gpt-5'
    })

    logger.log('股票分析结果', { chatId, userId, result: analysisResult })
    return analysisResult

  } catch (error) {
    logger.error('多模态股票分析失败', {
      chatId,
      userId
    })
    eventTrackClient.track(chatId, '股票多模态分析失败', {
      message_count: imageAndText.length
    })
    return ''
  }
}

export async function getVideoFileSize(url: string) {
  try {
    const res = await axios.head(url, { timeout: 5000 })
    const length = res.headers['content-length']
    return length ? parseInt(length, 10) : null
  } catch (error) {
    logger.warn(`HEAD 请求失败，无法获取视频文件大小: ${error}`)
    return null
  }
}

export async function handleVideoMessage(videoUrl: string, chatId: string) {
  const userId = getUserId(chatId)
  const dashscopeApiKey = Config.setting.qwen.apiKey || process.env.DASHSCOPE_API_KEY

  const openai = new OpenAI({
    apiKey: dashscopeApiKey,
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  })

  try {
    const sizeBytes = await getVideoFileSize(videoUrl)
    if (sizeBytes !== null && sizeBytes > MAX_VIDEO_SIZE) {
      throw new Error(`视频大小 ${ (sizeBytes / (1024 * 1024)).toFixed(2) } MB 超过 150 MB 限制`)
    }
    const messages: any = [
      {
        'role': 'user',
        'content': [{
          'type': 'video_url',
          'video_url': { 'url': videoUrl },
        },
        { 'type': 'text', 'text': '请以【视频】开头，然后分析视频的内容是什么，输出一段话，请不要使用markdown格式' }]
      }]
    const qwenResponse = await openai.chat.completions.create({
      model: 'qwen-omni-turbo',
      messages: messages,
      max_completion_tokens: 512,
      stream: true,
      stream_options: {
        include_usage: true
      },
      modalities: ['text']
    })
    let qwenResponseText = ''
    for await (const chunk of qwenResponse) {
      qwenResponseText += chunk.choices[0]?.delta.content || ''
    }
    qwenResponseText = qwenResponseText.trim()
    if (!qwenResponseText.startsWith('【视频】')) { qwenResponseText = `【视频】${qwenResponseText}` }
    qwenResponseText = qwenResponseText.replace(/\n/g, '')

    // await YuHeHumanTransfer.transfer(chatId, userId, YuHeHumanTransferType.ProcessVideo, 'onlyNotify', qwenResponseText)
    eventTrackClient.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(
        HumanTransferType,
        HumanTransferType.UnknownMessageType
      ),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(
        IWecomReceivedMsgType,
        IWecomReceivedMsgType.Video
      ),
    })

    return qwenResponseText
  } catch (error) {
    logger.warn(`处理视频消息时出错: ${error}`)
    await HumanTransfer.transfer(chatId, userId, HumanTransferType.ProcessVideoFailed, true)
    eventTrackClient.track(chatId, IEventType.TransferToManual, {
      reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
      video_url: videoUrl,
      msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, IWecomReceivedMsgType.Video),
    })
    return ''
  }
}

export async function handleUnknownMessage(message: IReceivedMessage) {
  if (!message.imContactId) { return }

  const chat_id = getChatId(message.imContactId)
  await HumanTransfer.transfer(chat_id, message.imContactId, HumanTransferType.UnknownMessageType)

  eventTrackClient.track(chat_id,
    IEventType.TransferToManual,
    { reason: ObjectUtil.enumValueToKey(HumanTransferType,  HumanTransferType.UnknownMessageType),
      message: JSON.stringify(message), msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.messageType)
    })

  logger.warn(`未知消息，请及时处理：${message.messageType}`)
}


// 处理微信欢迎语
export async function handleWelcomeMessage(userId: string) {
  const chatId = getChatId(userId)
  const flags = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
  if (flags.is_friend_accepted) {
    return
  }

  // 如果没有通过好友事件的话，进入欢迎语流程
  await sendHaoguWelComeMessage(getChatId(userId), userId)
}