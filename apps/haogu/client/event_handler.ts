import dayjs from 'dayjs'
import { Config } from 'config'
import { getChatId } from 'config/chat_id'
import { catchError } from 'lib/error/catchError'
import { JuziAPI } from 'model/juzi/api'
import { FriendAcceptedEvent, JuziEventHandler } from 'service/message_handler/juzi/event_handler'
import { startTasks } from 'service/visualized_sop/visualized_sop_task_starter'
import { IChattingFlag } from '../state/user_flags'
import {
  IXingyanPushCallback,
} from 'model/xingyan'
import { AsyncLock } from 'model/lock/lock'

import { PrismaMongoClient } from '../database/prisma'
import { ExtractUserSlots } from '../user_slots/user_slots_extraction'
import { calTaskTime } from '../visualized_sop/visualized_sop_processor'
import { chatStateStoreClient, chatDBClient } from '../service/base_instance'
import { wecomCommonMessageSender } from '../service/send_message_instance'

export class Event<PERSON>and<PERSON> extends JuziEventHandler {
  async handleFriendAcceptedEvent(data: FriendAcceptedEvent) {
    // 只处理一次加好友事件
    const userId = data.imContactId
    const chatId = getChatId(userId)
    const isFriendAccepted = (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_friend_accepted
    if (isFriendAccepted) {
      return
    }
    // 初始化课程号

    // 这里直接调用一下 getCustomerInfo，防止被客户删除掉获取不到客户信息
    await catchError(JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId))

    await chatStateStoreClient.update(chatId, {
      state: {
        is_friend_accepted: true
      }
    })

    await sendHaoguWelComeMessage(chatId, userId)

    // 直接在这里更新客户名
    await chatDBClient.updateContact(chatId, data.imContactId, data.name)
  }
  /*
 * 按照是否是课程前一周、课程周、课程结束后的第几周，1-7 进行 + 时间 进行任务创建。
 */


  handleYuHeEvent(callback: IXingyanPushCallback<any>) {
    switch (callback.type) {
      default:
        throw new Error(`不支持的回调类型: ${callback.type}`)
    }
  }

  /**
   * 处理客户进入直播间事件
   * @param callback 回调数据
   */
  async handleUserEnterRoom(callback: any) {
    console.log('客户进入直播间:', JSON.stringify(callback, null, 4))
    const chat = await chatDBClient.getChatByPhone(callback.param.mobile)
    if (!chat) {
      return
    }

    const chatId =  chat.id

    await chatStateStoreClient.update(chatId, {
      state: {
        is_in_live_room: true
      }
    })
  }

  /**
   * 处理客户离开直播间事件
   * @param callback 回调数据
   */
  async handleUserLeaveRoom(callback: any) {
    console.log('客户离开直播间:', JSON.stringify(callback, null, 4))
    const chat = await chatDBClient.getChatByPhone(callback.param.mobile)
    if (!chat) { return }

    const chatId =  chat.id
    await chatStateStoreClient.update(chatId, {
      state: {
        is_in_live_room: false
      }
    })

    // TODO:客户离开直播间超过5分钟，发送提醒
  }

  /**
   * 处理商品购买事件
   * @param callback 回调数据
   */
  async handleProductPurchase(callback: any) {
    console.log('商品购买:', JSON.stringify(callback, null, 4))
  }

  /**
   * 处理商品点击事件
   * @param callback 回调数据
   */
  async handleProductClick(callback: any) {
    console.log('商品点击:', JSON.stringify(callback, null, 4))
  }

  async handleOrderClose(callback: any) {
    console.log('订单关闭:', JSON.stringify(callback, null, 4))
  }

}

export async function sendHaoguWelComeMessage(chatId:string, userId:string) {
  const lock = new AsyncLock()
  await lock.acquire(chatId, async () => { // 如果有新消息，当前回复会被丢弃
    await chatStateStoreClient.update(chatId, {
      state: {
        is_friend_accepted: true
      }
    })

    const mongoClient = PrismaMongoClient.getInstance()
    const chat = await chatDBClient.getById(chatId)

    if (chat) {
      const userSlotService = new ExtractUserSlots()
      userSlotService.extractUserSlotsFromChatHistory({ chatId, chatHistory:[{
        role: 'user',
        date: dayjs().format('YYYY-MM-DD'),
        message: chat.contact.wx_name
      }], topicRecommendations: userSlotService.getTopicRecommendations(), topicRules:userSlotService.getTopicRules() })
      // 更新备注
      await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId,  dayjs().format('YYMMDD') + chat.contact.wx_name))
    }

    await startTasks(chatStateStoreClient, 'haogu', userId, chatId, calTaskTime)

    await wecomCommonMessageSender.sendText(chatId, {
      text: '《欢迎》',
      description: '欢迎语'
    })

    // await startAskIntention(chatId)

  }, { timeout: 2 * 60 * 1000 })
}

// export async function startAskIntention(chatId:string) {
//   await chatStateStoreClient.update(chatId, {
//     nextStage: Node.IntentionQuery
//   })
// }