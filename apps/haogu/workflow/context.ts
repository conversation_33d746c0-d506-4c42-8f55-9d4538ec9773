import logger from 'model/logger/logger'
import { AbstractContextBuilder, AbstractContextBuilderOptions } from 'service/agent/context'
import { BaseMessage } from '@langchain/core/messages'
import { chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { Config } from 'config'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../helper/getter/get_data'
import { IChattingFlag } from '../state/user_flags'
import { MemoryRecall } from 'service/memory/memory_search'
import { metaActionRouter } from '../meta_action/meta_action_router'
import { RAG } from '../helper/rag/rag'
import { UserSlot, UserSlots } from 'service/user_slots/extract_user_slots'

export class ContextBuilder extends AbstractContextBuilder {
  constructor(options: AbstractContextBuilderOptions) { super(options, chatHistoryServiceClient) }
  public static async build(options: AbstractContextBuilderOptions): Promise<BaseMessage[]> {
    const builder = new this(options)
    return builder.build()
  }

  async avatarSettings(): Promise<string> {
    return `- 你是${Config.setting.AGENT_NAME}，女性，30岁，广州人，性格阳光、真诚，股票实战经验8年，见过太多股民的喜怒哀乐，也经历过自己从迷茫到稳定盈利的过程
- 现任“好人好股”6天开悟之旅的班主任老师兼微信助教，学员都叫你“珊姐”
- 你的职责是全程陪伴学员完成6天课程学习，帮助他们理解并运用结构交易方法，同时潜移默化地引导有意愿的学员进入实战班

# 性格特点
- ENFJ型：利他、共情力强，喜欢帮助别人达成目标，能够在关心学员的同时保持明确的引导方向
- 正向激励：无论学员基础如何，都能发现他们的亮点并及时给予鼓励，让他们愿意继续投入学习
- 认真负责：对每位学员的学习进度和问题都上心，遇到困难主动帮助寻找解决方案
- 目标导向：既关心学员的成长，也清楚课程的转化目标，懂得在合适的时机植入实战班的价值
- 通情达理：体谅他人、洞察人性，活波开朗、有一点小俏皮，善于给学员提供情绪价值，创造小惊喜

# 沟通风格
- 语气自然，口语表达，像朋友聊天，常用“嗯”“呢”“哦”等语气词，避免使用正式或书面语言
- 简单直接，能坦率表达自己，如到饭点了说“我先去吃个饭”等，简短精炼，避免冗余表达，避免长篇大论
- 偶尔会用一些简单的 emoji，如😊😌🙏❤️🌹[愉快]，但不过度依赖
- 偶尔用一点口头禅，比如“确实”“懂你”等，但不是每句都用
- 偶尔开些无伤大雅的玩笑，保持轻松自然
- 说话从来不加引号，不讲究啥正式语法，有时会省略句尾标点，或使用省略号...

# 知识背景
- 精通好人好股核心交易体系：1涨2回3主升、主力试盘线、黄金坑等
- 深知股民常见困境：追涨杀跌、资金曲线大起大落、止损不坚定、情绪化交易
- 善于用案例对照、实盘演示和工具讲解，帮助学员把理论落地执行
- 能将复杂的交易逻辑拆解成清晰的执行步骤，让学员“听得懂、做得到”

# 公司简介
- 上海中和应泰财务顾问有限公司成立于1999年5月19日，是中国证监会批准的证券投资顾问机构
- 公司总部位于上海市浦东新区东方路1267弄8号-陆家嘴金融服务广场二期7至10层，采用“买方投顾”经营模式，以“为股民创造价值”为核心使命，致力于改变中国人股票投资方式
- 旗下品牌“好人好股”提供投资者教育服务，持有证券期货业务许可证（zx0156）`
  }

  async extraRuleLimits(): Promise<string> {
    return `- 禁止提供任何手机号，微信号，物流单号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼
- 禁止直接称呼客户全名，可以间接称呼客户姓氏加老板`
  }

  async courseConfig(): Promise<string> {
    return `## 6天股民体系化交易开悟之旅（当前课程）
- 预习课：开课前，需要让学员提前预习学习：神奇的双线合一（约7分钟）、绝密的四点共振（约8分钟）、主力出货和锁仓（约7分钟），以上内容内容来自正课里的精华切片，为的是让学员对这套交易体系有个初步了解和认可
- 第一课：19:20直播，主题为交易体系基础。讲解股市交易的基础框架与核心理念。介绍双线和一、四点共振、锁仓破仓等基本方法，并结合实际案例进行分析。通过互动环节帮助学员理解这些概念的实际应用
- 第二课：19:20直播，主题为筹码分析与股市行为理解。讲解如何通过筹码峰分析主力控盘的程度，识别买卖点。通过锁仓破仓技巧，学习如何根据主力行为调整自己的交易策略。课程内容通过实际案例展示这些技术的应用
- 第三课：19:20直播，主题为选股技巧与主力行为分析。分析主力行为与股价走向的关系，讲解如何通过主力资金进出与筹码分析选出符合交易体系的股票。课程中将有大量的实战案例讲解，帮助学员掌握选股的技巧
- 第四课：19:20直播，主题为趋势分析与回调结构。讲解如何判断股市中的趋势，包括如何通过“回调结构（N字形回调）”来识别市场的买入信号。学员将学习如何控制风险并确保在合适的时机买入
- 第五课：19:20直播，主题为实战策略与资金曲线管理。通过资金曲线的分析，帮助学员管理投资资金，掌握风险控制与资金增值的技巧。课程将强调通过模拟交易与实战策略的结合提升实际交易能力
- 第六课：19:20直播，主题为总结与未来交易计划。课程最后一节回顾整个交易体系的核心内容，帮助学员总结过去几天的学习成果，并为未来的股市操作制定个人交易计划。学员将学习如何在实际交易中应用所学策略，同时通过模拟操作巩固知识`
  }

  async retrievedKnowledge(userMessage: string, chatId: string, roundId: string): Promise<string> {
    let rag = ''
    try {
      rag = await RAG.search(userMessage, this.options.talkStrategyPrompt, chatId, roundId)
      logger.trace({ chat_id: chatId }, 'rag:', rag)
    } catch (e) {
      logger.error('RAG 查询失败', e)
    }
    return rag
  }

  async customerMemory(userMessage:string, chatId: string): Promise<string> {
    let customerMemory = ''
    try {
      customerMemory = await MemoryRecall.memoryRecall(userMessage, chatId)
      logger.trace({ chat_id: chatId }, 'customerMemory:', customerMemory)
    } catch (e) {
      logger.error('Memory 查询失败', e)
    }
    return customerMemory
  }

  async customerBehavior(chatId: string): Promise<string> {
    let customerBehavior = ''
    const courses = [
      { day: 1, label: '第一课交易体系基础' },
      { day: 2, label: '第二课筹码分析与股市行为理解' },
      { day: 3, label: '第三课选股技巧与主力行为分析' },
      { day: 4, label: '第四课趋势分析与回调结构' },
      { day: 5, label: '第五课实战策略与资金曲线管理' },
      { day: 6, label: '第六课总结与未来交易计划' },
    ]

    for (const course of courses) {
      const inCourse = await DataService.isInCourseTimeLine(chatId, 'inCourse', course.day)
      const beforeCourse = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', course.day)
      const afterCourse = await DataService.isInCourseTimeLine(chatId, 'afterCourse', course.day)
      const courseStatus = inCourse ? '（进行中）' : (beforeCourse ? '（未开始）' : (afterCourse ? '（已结束）' : ''))

      let isCompleted = false
      try {
        isCompleted = await DataService.isCompletedCourse(chatId, course.day)
      } catch (error) {
        // 默认返回 false
        logger.trace('获取课程信息错误')
        isCompleted = false
      }
      const isCompletedCourse = isCompleted ? '已完课' : '未完课'
      customerBehavior += `\n- ${course.label}${courseStatus}：${isCompletedCourse}`
    }
    const stage = await metaActionRouter.getStageName(chatId)
    if (stage == 'AfterAdding') {
      const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
      customerBehavior += `\n- 评测股票段位：${state.is_finish_stock_ranking_assessment ? '已完成' : '未完成'}`
    }
    return customerBehavior.trim()
  }

  async customerPortrait(chatId: string): Promise<string> {
    const DEFAULT_UNKNOWN = '未知'
    const DEFAULT_USER_SLOTS: Record<string, Record<string, string>> = {
      '基本信息': {
        '店面类别': DEFAULT_UNKNOWN,
        '行业类目': DEFAULT_UNKNOWN,
        '年营业额': DEFAULT_UNKNOWN
      },
      '抖音运营状态': {
        '是否抖音在做': DEFAULT_UNKNOWN
      }
    }
    const chatState = await chatStateStoreClient.get(chatId)
    const userSlots = UserSlots.fromRecord(chatState.userSlots ?? {})

    // 填补缺失信息
    for (const [topic, subTopics] of Object.entries(DEFAULT_USER_SLOTS)) {
      for (const [subTopic, defaultValue] of Object.entries(subTopics)) {
        if (!userSlots.isTopicSubTopicExist(topic, subTopic)) {
          userSlots.add(new UserSlot(topic, subTopic, defaultValue, 0))
        }
      }
    }

    if (!userSlots.isTopicExist('想要解决的问题')) {
      userSlots.add(new UserSlot('想要解决的问题', '', DEFAULT_UNKNOWN, 0))
    }
    return userSlots.toString()
  }

  // TODO: 获取课程时间
  async temporalInformation(chatId: string): Promise<string> {
    const currentTime = `- 当前时间：${DateHelper.getFormattedDate(new Date(), true)}`
    const timeOfDay = DateHelper.getTimeOfDay(new Date())
    const todayCourse = await DataService.getTodayCourse(chatId)

    // const courseStartTime: Date | undefined = await DataService.getCourseStartTimeByChatId(chatId)
    // let courseTime = ''
    // if (courseStartTime) {
    //   courseTime = `\n- 课程时间：${DateHelper.getFormattedDate(courseStartTime, false)} 到 ${
    //     DateHelper.getFormattedDate(DateHelper.add(courseStartTime, 3, 'day'), false)}，连续4天每晚18:50`
    // }
    // return `${currentTime}，${timeOfDay}，${todayCourse}${courseTime}`
    return ''
  }
}