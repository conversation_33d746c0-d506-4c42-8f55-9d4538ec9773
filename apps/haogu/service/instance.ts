import { PrismaMongoClient } from '../database/prisma'
import { EventTracker } from 'model/logger/data_driven'
import { MemoryStore } from 'service/memory/memory_store'
import { LLMReply } from 'service/llm/llm_reply'
import { HaoguVisualizedSopProcessor } from '../visualized_sop/visualized_sop_processor'
import { HaoguVisualizedGroupSopProcessor } from '../visualized_sop/visualized_group_sop_processor'
import { BaseHumanTransfer } from 'service/human_transfer/human_transfer'
import { chatHistoryServiceClient, chatStateStoreClient, chatDBClient } from './base_instance'
import { wecomCommonMessageSender, wecomMessageSender } from './send_message_instance'

export const memoryStoreClient = new MemoryStore(chatHistoryServiceClient, chatStateStoreClient)
export const llmReplyClient = new LLMReply(chatDBClient, chatHistoryServiceClient)
export const haoguVisualizedSopProcessor = new HaoguVisualizedSopProcessor('haogu', chatDBClient, chatHistoryServiceClient, wecomCommonMessageSender)
export const haoguVisualizedGroupSopProcessor = new HaoguVisualizedGroupSopProcessor('haogu', PrismaMongoClient.getCommonInstance(), wecomMessageSender)
export const humanTransferClient = new BaseHumanTransfer(chatDBClient, chatStateStoreClient)