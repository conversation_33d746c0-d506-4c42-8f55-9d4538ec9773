import { getUserId } from 'config/chat_id'
import { conditionJudgeMap, linkSourceVariableTagMap, textVariableMap } from '../visualized_sop_variable'

const chatId = '7881301356036850_1688858213716953'
describe('test', () => {
  test('variable condition', async() => {
    const userId = getUserId(chatId)
    for (const name in conditionJudgeMap) {
      try {
        console.log(name, await conditionJudgeMap[name]({ chatId, userId }))
      } catch (e) {
        console.log(e)
      }
    }
  }, 600000)
  test('variable text variable', async() => {
    const userId = getUserId(chatId)
    for (const name in textVariableMap) {
      try {
        console.log(name, await textVariableMap[name]({ chatId, userId }))
      } catch (e) {
        console.log(e)
      }
    }
  }, 600000)
  test('variable linkSource variable', async() => {
    const userId = getUserId(chatId)
    for (const name in linkSourceVariableTagMap) {
      try {
        console.log(name, await linkSourceVariableTagMap[name]({ chatId, userId }))
      } catch (e) {
        console.log(e)
      }
    }
  }, 6000000)
})