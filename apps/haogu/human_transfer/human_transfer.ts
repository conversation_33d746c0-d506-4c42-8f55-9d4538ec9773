import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'
import logger from 'model/logger/logger'
import { accountToName } from '../config/config'
import { humanTransferClient } from '../service/instance'
import { eventTrackClient } from '../service/send_message_instance'

export enum HumanTransferType {
  UnknownMessageType = 1,
  ProcessImage = 2,
  MessageSendFailed = 3,
  HesitatePayment = 4,
  ExecutePostpone = 5,
  VoiceOrVideoCall = 6,
  SoftwareIssue = 7,
  RobotDetected = 8,
  PaidCourse = 9,
  ProcessVideo = 10,
  ProcessVideoFailed = 11,
  ExplicitlyPurchases = 12,
  ExecuteRefresherTraining= 13
}

/**
 * 宇和项目转人工处理
 */
export class HumanTransfer {
  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param transferType
   * @param toHuman
   * @param additionalMsg
   */
  public static async transfer(chatId: string, userId: string, transferType: HumanTransferType, toHuman: boolean | 'onlyNotify' = true, additionalMsg?: string) {
    if (userId === 'null') {
      logger.error('[HaoguHumanTransfer] userId is null', transferType)
      return
    }

    if (transferType !== HumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的 日志在上级进行处理，这里不进行重复处理
      eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, transferType) })
    }

    // 拼接要发送的消息
    const ta_id = chatId.split('_')[1]
    const accountName = accountToName[ta_id] ?? '未知'
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'
    const notificationMessages = {
      [HumanTransferType.UnknownMessageType]: '客户发了一个文件',
      [HumanTransferType.ProcessImage]: '客户发了一张【图片】',
      [HumanTransferType.ProcessVideo]: '客户发了一个【视频】',
      [HumanTransferType.ProcessVideoFailed]: '客户发了一个【视频】，识别失败',
      [HumanTransferType.SoftwareIssue]: '客户软件或者课程链接出问题',
      [HumanTransferType.MessageSendFailed]: '消息发送失败',
      [HumanTransferType.RobotDetected]: '客户识别到了AI',
      [HumanTransferType.HesitatePayment] : '客户支付犹豫',
      [HumanTransferType.PaidCourse]: '[烟花]客户已支付[烟花]',
      [HumanTransferType.ExplicitlyPurchases]: '客户弹幕识别出支付意向',
      [HumanTransferType.ExecutePostpone]: '客户已延期',
      [HumanTransferType.ExecuteRefresherTraining]: '客户已复训',
      [HumanTransferType.VoiceOrVideoCall]: '客户发起语音/视频通话'
    }
    const message = `（${accountName}）${  notificationMessages[transferType] as string  }，${handleType  }${additionalMsg ? `\n${additionalMsg}` : ''}`
    return await humanTransferClient.transfer(chatId, userId, message, toHuman)
  }
}