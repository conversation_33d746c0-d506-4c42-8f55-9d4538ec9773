import { DataService } from '../../helper/getter/get_data'
import { MetaActions, ThinkPrompt } from '../meta_action'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'

export class DuringCourse extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'inCourse') && !await DataService.isInCourseTimeLine(chatId, 'afterSales')
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.duringCourse)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.duringCourse)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}