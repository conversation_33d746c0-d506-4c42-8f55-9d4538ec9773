import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterCourse1 extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    return await DataService.isInCourseTimeLine(chatId, 'afterSales', 1)
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
      '发送回放': AfterCourse1.sendCourseReplay,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourse1)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourse1)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }

  private static async sendCourseReplay(chat_id: string) {
    const course1Backup = await DataService.getCourseLink(chat_id, 1) ?? ''
    const actionInfo: IActionInfo = { guidance: `给客户发送课程回放${course1Backup}`, }
    return actionInfo
  }
}