import { chatStateStoreClient } from '../../service/base_instance'
import { DataService } from '../../helper/getter/get_data'
import { IActionInfo, MetaActionComponent } from 'service/agent/meta_action/component'
import { IChattingFlag } from '../../state/user_flags'
import { MetaActions, ThinkPrompt } from '../meta_action'

export class AfterBonding extends MetaActionComponent {
  async activeStatus(chatId: string): Promise<boolean> {
    const chatState = await chatStateStoreClient.get(chatId)
    return await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1) && (chatState.state as IChattingFlag).after_bonding as boolean
  }

  getAction(): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      // '发送成功案例': PostAction.sendCaseImage,
      // '提供延期方案': PostAction.enterPostpone,
    }
    return Promise.resolve(actionMap)
  }

  getMetaAction(): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterBonding)
  }

  getThinkPrompt(): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterBonding)
  }

  getGuidance(): Promise<string> {
    return Promise.resolve('')
  }
}