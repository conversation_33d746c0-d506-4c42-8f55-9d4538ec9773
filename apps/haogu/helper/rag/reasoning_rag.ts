import { Rag<PERSON>rom<PERSON> } from './rag_prompt'
import { LLM } from 'lib/ai/llm/llm_model'
import { RagToolExecuteParams, ReasoningRagTool } from './reasoning_rag_tool'


export class ReasoningRag {

  public static async isNeedReasoning(chatHistory: string, ragContext: string, roundId: string) {
    const prompt = await RagPrompt.isNeedReasoningRag(chatHistory, ragContext)
    const response = await LLM.predict(prompt, { meta:{ round_id:roundId }, responseJSON: true, model:'gpt-5-mini' })

    const jsonRes = JSON.parse(response)
    const verifyRes = jsonRes.verdict

    return verifyRes === 'YES'

  }




  public static async reasoningRag(chatHistory: string, chatId: string, strategy: string, ragContext: string, roundId: string) {
    let context = `针对客户问题rag搜索的结果：${ragContext}`
    let iterations = 0
    const maxIterations = 2
    while (iterations < maxIterations) {
      iterations++
      // Build a reasoning prompt. We assume RagPrompt.reasoningRag exists and
      // accepts the chat history, current context, strategy and the list of tools.
      const prompt = await RagPrompt.reasoningRag(chatHistory, context, strategy, await ReasoningRagTool.getTools())
      const llmResponse = await LLM.predict(prompt, { meta: { round_id: roundId }, responseJSON: true })

      const parsed = JSON.parse(llmResponse)
      const thought: string = parsed.thought || ''
      const actionName: string = parsed.action
      const actionInput = parsed.action_input
      const finish: string | undefined = parsed.finish
      // If the model has produced a final answer, return it.
      if (finish) {
        return finish
      }
      // Find the requested tool. If it does not exist, record an observation and abort.
      const tool = await ReasoningRagTool.getToolByKey(actionName)
      if (!tool) {
        const obs = `Tool '${actionName}' not found.`
        context += `\nObservation: ${obs}`
        break
      }
      // Execute the tool and record its output.
      const param = {
        chatId: chatId,
        roundId: roundId,
        ...actionInput
      } as RagToolExecuteParams
      const observation = await tool.execute(param)
      // Append the thought, action, and observation to the context for the next iteration.
      context += `\nThought: ${thought}\nAction: ${actionName}[${JSON.stringify(actionInput)}]\nObservation: ${observation}`
    }
    // 超出最大迭代长度强制回答
    // const res = await this.getFinalAnswer(chatHistory, context, roundId)
    // `针对客户问题的答案：\n${res}`
    return ''
  }


  private static async getFinalAnswer(chatHistory: string, context: string, roundId: string) {

    const prompt = await RagPrompt.finalAnswer(chatHistory, context)
    return await LLM.predict(prompt, { meta:{ round_id:roundId }, model:'gpt-5-mini' })

  }




}