'use client'

import { useSession } from 'next-auth/react'
import { usePathname } from 'next/navigation'
import { MdOutlineViewTimeline } from 'react-icons/md'

export interface MenuItem {
  name: string;
  link: string;
  icon: React.ReactNode;
  children?: MenuItem[];
}

function SideBarMenuItem({ menuItem }: { menuItem: MenuItem }) {
  const pathname = usePathname()
  if (menuItem.children) {
    return (
      <li>
        <details open={pathname.startsWith(menuItem.link)}>
          <summary>
            {menuItem.icon}
            {menuItem.name}
          </summary>
          <ul>
            {menuItem.children &&
              menuItem.children.map((child, index) => (
                <SideBarMenuItem key={index} menuItem={child} />
              ))}
          </ul>
        </details>
      </li>
    )
  } else {
    return (
      <li>
        <a
          href={menuItem.link}
          className={pathname == menuItem.link ? 'menu-active' : ''}
        >
          {menuItem.icon}
          {menuItem.name}
        </a>
      </li>
    )
  }
}

export function Menu({ menu }: { menu: MenuItem[] }) {
  return (
    <ul className="menu sticky top-[calc(3rem)] min-h-[calc(100dvh-3rem)] w-56 flex-none self-start border border-t-0 border-l-0 border-base-200 bg-base-200 pt-[1rem]">
      {menu.map((item, index) => (
        <SideBarMenuItem menuItem={item} key={index} />
      ))}
    </ul>
  )
}
const menu: MenuItem[] = [
  {
    name: '宇合',
    link: '/yuhe',
    icon: <MdOutlineViewTimeline />,
    children: [
      {
        name: 'rag',
        link: '/yuhe/rag',
        icon: <MdOutlineViewTimeline />,
        children: [
          {
            name: 'QA',
            link: '/yuhe/rag/QA',
            icon: <MdOutlineViewTimeline />,
          },
          {
            name: 'sales case',
            link: '/yuhe/rag/case',
            icon: <MdOutlineViewTimeline />,
          }
        ]
      },
      {
        name: 'sop',
        link: '/yuhe/sop',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'group',
        link: '/yuhe/group',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'group sop',
        link: '/yuhe/group_sop',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'user',
        link: '/yuhe/user',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'analysis',
        link: '/yuhe/analysis',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'sop_analysis',
        link: '/yuhe/sop_analysis',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'teacher',
        link: '/yuhe/teacher',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'dashboard',
        link: '/yuhe/dashboard',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'ip manage',
        link: '/yuhe/ip',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'script',
        link: '/yuhe/script',
        icon: <MdOutlineViewTimeline />,
      },
    ],
  },
  {
    name: 'moer海外',
    link: '/moer_overseas',
    icon: <MdOutlineViewTimeline />,
    children: [
      {
        name: 'user',
        link: '/moer_overseas/user',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'sop',
        link: '/moer_overseas/sop',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'rag',
        link: '/moer_overseas/rag',
        icon: <MdOutlineViewTimeline />,
        children: [
          {
            name: 'QA',
            link: '/moer_overseas/rag/QA',
            icon: <MdOutlineViewTimeline />,
          },
        ]
      },
    ],
  },
  {
    name: 'haogu',
    link: '/haogu',
    icon: <MdOutlineViewTimeline />,
    children: [
      {
        name: 'user',
        link: '/haogu/user',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'sop',
        link: '/haogu/sop',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'rag',
        link: '/haogu/rag',
        icon: <MdOutlineViewTimeline />,
        children: [
          {
            name: 'QA',
            link: '/haogu/rag/QA',
            icon: <MdOutlineViewTimeline />,
          },
        ]
      },
    ],
  },
  {
    name: 'script',
    link: '/script',
    icon: <MdOutlineViewTimeline />,
  }
]

const visitorMenu: MenuItem[] = [
  {
    name: '宇合',
    link: '/yuhe',
    icon: <MdOutlineViewTimeline />,
    children: [
      {
        name: 'user',
        link: '/yuhe/user',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'analysis',
        link: '/yuhe/analysis',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'ip manage',
        link: '/yuhe/ip',
        icon: <MdOutlineViewTimeline />,
      },
      {
        name: 'teacher',
        link: '/yuhe/teacher',
        icon: <MdOutlineViewTimeline />,
      }
    ],
  },
  {
    name: 'moer海外',
    link: '/moer_overseas',
    icon: <MdOutlineViewTimeline />,
    children: [
      {
        name: 'user',
        link: '/moer_overseas/user',
        icon: <MdOutlineViewTimeline />,
      },
    ],
  },
]

export function MyMenu() {
  'use client'
  const pathname = usePathname()
  const { data:session } = useSession()
  if (pathname != '/login') {
    if (session?.user?.name == 'freespirit') {
      return <Menu menu={menu}/>
    } else {
      return <Menu menu={visitorMenu}/>
    }
  } else {
    return <></>
  }
}
